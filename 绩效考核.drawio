<mxfile host="65bd71144e">
    <diagram id="7dOI8p88FT4vELaQ-aaH" name="第 1 页">
        <mxGraphModel dx="2286" dy="1989" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="827" pageHeight="1169" math="0" shadow="0">
            <root>
                <mxCell id="0"/>
                <mxCell id="1" parent="0"/>
                <mxCell id="sBk4UZyAnzUuyrO-kaQZ-597" value="" style="edgeStyle=entityRelationEdgeStyle;fontSize=12;html=1;endArrow=ERzeroToMany;endFill=1;entryX=0.05;entryY=0.5;entryDx=0;entryDy=0;entryPerimeter=0;" parent="1" edge="1">
                    <mxGeometry width="100" height="100" relative="1" as="geometry">
                        <mxPoint x="520" y="50" as="sourcePoint"/>
                        <mxPoint x="620" y="536" as="targetPoint"/>
                        <Array as="points">
                            <mxPoint x="641" y="430"/>
                            <mxPoint x="610" y="555"/>
                            <mxPoint x="610" y="565"/>
                            <mxPoint x="610" y="615"/>
                            <mxPoint x="620" y="615"/>
                            <mxPoint x="600" y="605"/>
                        </Array>
                    </mxGeometry>
                </mxCell>
                <mxCell id="2" value="项目" style="shape=table;startSize=30;container=1;collapsible=1;childLayout=tableLayout;fixedRows=1;rowLines=0;fontStyle=1;align=center;resizeLast=1;" parent="1" vertex="1">
                    <mxGeometry x="80" y="230" width="180" height="150" as="geometry"/>
                </mxCell>
                <mxCell id="3" value="" style="shape=tableRow;horizontal=0;startSize=0;swimlaneHead=0;swimlaneBody=0;fillColor=none;collapsible=0;dropTarget=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;top=0;left=0;right=0;bottom=1;" parent="2" vertex="1">
                    <mxGeometry y="30" width="180" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="4" value="PK" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;fontStyle=1;overflow=hidden;" parent="3" vertex="1">
                    <mxGeometry width="30" height="30" as="geometry">
                        <mxRectangle width="30" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="5" value="项目ID" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;align=left;spacingLeft=6;fontStyle=5;overflow=hidden;" parent="3" vertex="1">
                    <mxGeometry x="30" width="150" height="30" as="geometry">
                        <mxRectangle width="150" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="6" value="" style="shape=tableRow;horizontal=0;startSize=0;swimlaneHead=0;swimlaneBody=0;fillColor=none;collapsible=0;dropTarget=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;top=0;left=0;right=0;bottom=0;" parent="2" vertex="1">
                    <mxGeometry y="60" width="180" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="7" value="" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;editable=1;overflow=hidden;" parent="6" vertex="1">
                    <mxGeometry width="30" height="30" as="geometry">
                        <mxRectangle width="30" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="8" value="项目名称" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;align=left;spacingLeft=6;overflow=hidden;" parent="6" vertex="1">
                    <mxGeometry x="30" width="150" height="30" as="geometry">
                        <mxRectangle width="150" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="9" value="" style="shape=tableRow;horizontal=0;startSize=0;swimlaneHead=0;swimlaneBody=0;fillColor=none;collapsible=0;dropTarget=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;top=0;left=0;right=0;bottom=0;" parent="2" vertex="1">
                    <mxGeometry y="90" width="180" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="10" value="" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;editable=1;overflow=hidden;" parent="9" vertex="1">
                    <mxGeometry width="30" height="30" as="geometry">
                        <mxRectangle width="30" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="11" value="项目简介" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;align=left;spacingLeft=6;overflow=hidden;" parent="9" vertex="1">
                    <mxGeometry x="30" width="150" height="30" as="geometry">
                        <mxRectangle width="150" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="12" value="" style="shape=tableRow;horizontal=0;startSize=0;swimlaneHead=0;swimlaneBody=0;fillColor=none;collapsible=0;dropTarget=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;top=0;left=0;right=0;bottom=0;" parent="2" vertex="1">
                    <mxGeometry y="120" width="180" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="13" value="" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;editable=1;overflow=hidden;" parent="12" vertex="1">
                    <mxGeometry width="30" height="30" as="geometry">
                        <mxRectangle width="30" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="14" value="基本字段" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;align=left;spacingLeft=6;overflow=hidden;" parent="12" vertex="1">
                    <mxGeometry x="30" width="150" height="30" as="geometry">
                        <mxRectangle width="150" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="15" value="分类" style="shape=table;startSize=30;container=1;collapsible=1;childLayout=tableLayout;fixedRows=1;rowLines=0;fontStyle=1;align=center;resizeLast=1;" parent="1" vertex="1">
                    <mxGeometry x="330" y="30" width="180" height="150" as="geometry"/>
                </mxCell>
                <mxCell id="16" value="" style="shape=tableRow;horizontal=0;startSize=0;swimlaneHead=0;swimlaneBody=0;fillColor=none;collapsible=0;dropTarget=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;top=0;left=0;right=0;bottom=1;" parent="15" vertex="1">
                    <mxGeometry y="30" width="180" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="17" value="PK" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;fontStyle=1;overflow=hidden;" parent="16" vertex="1">
                    <mxGeometry width="30" height="30" as="geometry">
                        <mxRectangle width="30" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="18" value="分类ID" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;align=left;spacingLeft=6;fontStyle=5;overflow=hidden;" parent="16" vertex="1">
                    <mxGeometry x="30" width="150" height="30" as="geometry">
                        <mxRectangle width="150" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="19" value="" style="shape=tableRow;horizontal=0;startSize=0;swimlaneHead=0;swimlaneBody=0;fillColor=none;collapsible=0;dropTarget=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;top=0;left=0;right=0;bottom=0;" parent="15" vertex="1">
                    <mxGeometry y="60" width="180" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="20" value="FK" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;editable=1;overflow=hidden;" parent="19" vertex="1">
                    <mxGeometry width="30" height="30" as="geometry">
                        <mxRectangle width="30" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="21" value="项目ID" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;align=left;spacingLeft=6;overflow=hidden;" parent="19" vertex="1">
                    <mxGeometry x="30" width="150" height="30" as="geometry">
                        <mxRectangle width="150" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="22" value="" style="shape=tableRow;horizontal=0;startSize=0;swimlaneHead=0;swimlaneBody=0;fillColor=none;collapsible=0;dropTarget=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;top=0;left=0;right=0;bottom=0;" parent="15" vertex="1">
                    <mxGeometry y="90" width="180" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="23" value="" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;editable=1;overflow=hidden;" parent="22" vertex="1">
                    <mxGeometry width="30" height="30" as="geometry">
                        <mxRectangle width="30" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="24" value="分类名称" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;align=left;spacingLeft=6;overflow=hidden;" parent="22" vertex="1">
                    <mxGeometry x="30" width="150" height="30" as="geometry">
                        <mxRectangle width="150" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="25" value="" style="shape=tableRow;horizontal=0;startSize=0;swimlaneHead=0;swimlaneBody=0;fillColor=none;collapsible=0;dropTarget=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;top=0;left=0;right=0;bottom=0;" parent="15" vertex="1">
                    <mxGeometry y="120" width="180" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="26" value="" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;editable=1;overflow=hidden;" parent="25" vertex="1">
                    <mxGeometry width="30" height="30" as="geometry">
                        <mxRectangle width="30" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="27" value="基本字段" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;align=left;spacingLeft=6;overflow=hidden;" parent="25" vertex="1">
                    <mxGeometry x="30" width="150" height="30" as="geometry">
                        <mxRectangle width="150" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="28" value="文案" style="shape=table;startSize=30;container=1;collapsible=1;childLayout=tableLayout;fixedRows=1;rowLines=0;fontStyle=1;align=center;resizeLast=1;" parent="1" vertex="1">
                    <mxGeometry x="600" y="180" width="180" height="150" as="geometry"/>
                </mxCell>
                <mxCell id="29" value="" style="shape=tableRow;horizontal=0;startSize=0;swimlaneHead=0;swimlaneBody=0;fillColor=none;collapsible=0;dropTarget=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;top=0;left=0;right=0;bottom=1;" parent="28" vertex="1">
                    <mxGeometry y="30" width="180" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="30" value="PK" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;fontStyle=1;overflow=hidden;" parent="29" vertex="1">
                    <mxGeometry width="30" height="30" as="geometry">
                        <mxRectangle width="30" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="31" value="文案ID" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;align=left;spacingLeft=6;fontStyle=5;overflow=hidden;" parent="29" vertex="1">
                    <mxGeometry x="30" width="150" height="30" as="geometry">
                        <mxRectangle width="150" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="32" value="" style="shape=tableRow;horizontal=0;startSize=0;swimlaneHead=0;swimlaneBody=0;fillColor=none;collapsible=0;dropTarget=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;top=0;left=0;right=0;bottom=0;" parent="28" vertex="1">
                    <mxGeometry y="60" width="180" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="33" value="FK" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;editable=1;overflow=hidden;" parent="32" vertex="1">
                    <mxGeometry width="30" height="30" as="geometry">
                        <mxRectangle width="30" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="34" value="分类ID" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;align=left;spacingLeft=6;overflow=hidden;" parent="32" vertex="1">
                    <mxGeometry x="30" width="150" height="30" as="geometry">
                        <mxRectangle width="150" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="76" value="" style="shape=tableRow;horizontal=0;startSize=0;swimlaneHead=0;swimlaneBody=0;fillColor=none;collapsible=0;dropTarget=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;top=0;left=0;right=0;bottom=0;" parent="28" vertex="1">
                    <mxGeometry y="90" width="180" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="77" value="" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;editable=1;overflow=hidden;" parent="76" vertex="1">
                    <mxGeometry width="30" height="30" as="geometry">
                        <mxRectangle width="30" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="78" value="文案内容" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;align=left;spacingLeft=6;overflow=hidden;" parent="76" vertex="1">
                    <mxGeometry x="30" width="150" height="30" as="geometry">
                        <mxRectangle width="150" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="38" value="" style="shape=tableRow;horizontal=0;startSize=0;swimlaneHead=0;swimlaneBody=0;fillColor=none;collapsible=0;dropTarget=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;top=0;left=0;right=0;bottom=0;" parent="28" vertex="1">
                    <mxGeometry y="120" width="180" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="39" value="" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;editable=1;overflow=hidden;" parent="38" vertex="1">
                    <mxGeometry width="30" height="30" as="geometry">
                        <mxRectangle width="30" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="40" value="基本字段（可无需备注）" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;align=left;spacingLeft=6;overflow=hidden;" parent="38" vertex="1">
                    <mxGeometry x="30" width="150" height="30" as="geometry">
                        <mxRectangle width="150" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="43" value="音频" style="shape=table;startSize=30;container=1;collapsible=1;childLayout=tableLayout;fixedRows=1;rowLines=0;fontStyle=1;align=center;resizeLast=1;" parent="1" vertex="1">
                    <mxGeometry x="600" y="-110" width="180" height="270" as="geometry"/>
                </mxCell>
                <mxCell id="44" value="" style="shape=tableRow;horizontal=0;startSize=0;swimlaneHead=0;swimlaneBody=0;fillColor=none;collapsible=0;dropTarget=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;top=0;left=0;right=0;bottom=1;" parent="43" vertex="1">
                    <mxGeometry y="30" width="180" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="45" value="PK" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;fontStyle=1;overflow=hidden;" parent="44" vertex="1">
                    <mxGeometry width="30" height="30" as="geometry">
                        <mxRectangle width="30" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="46" value="音频ID" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;align=left;spacingLeft=6;fontStyle=5;overflow=hidden;" parent="44" vertex="1">
                    <mxGeometry x="30" width="150" height="30" as="geometry">
                        <mxRectangle width="150" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="56" value="" style="shape=tableRow;horizontal=0;startSize=0;swimlaneHead=0;swimlaneBody=0;fillColor=none;collapsible=0;dropTarget=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;top=0;left=0;right=0;bottom=0;" parent="43" vertex="1">
                    <mxGeometry y="60" width="180" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="57" value="FK" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;fontStyle=0;overflow=hidden;" parent="56" vertex="1">
                    <mxGeometry width="30" height="30" as="geometry">
                        <mxRectangle width="30" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="58" value="分类ID" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;align=left;spacingLeft=6;fontStyle=0;overflow=hidden;" parent="56" vertex="1">
                    <mxGeometry x="30" width="150" height="30" as="geometry">
                        <mxRectangle width="150" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="47" value="" style="shape=tableRow;horizontal=0;startSize=0;swimlaneHead=0;swimlaneBody=0;fillColor=none;collapsible=0;dropTarget=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;top=0;left=0;right=0;bottom=0;" parent="43" vertex="1">
                    <mxGeometry y="90" width="180" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="48" value="" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;editable=1;overflow=hidden;" parent="47" vertex="1">
                    <mxGeometry width="30" height="30" as="geometry">
                        <mxRectangle width="30" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="49" value="音频名称" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;align=left;spacingLeft=6;overflow=hidden;" parent="47" vertex="1">
                    <mxGeometry x="30" width="150" height="30" as="geometry">
                        <mxRectangle width="150" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="208" value="" style="shape=tableRow;horizontal=0;startSize=0;swimlaneHead=0;swimlaneBody=0;fillColor=none;collapsible=0;dropTarget=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;top=0;left=0;right=0;bottom=0;" parent="43" vertex="1">
                    <mxGeometry y="120" width="180" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="209" value="FK" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;fontStyle=0;overflow=hidden;" parent="208" vertex="1">
                    <mxGeometry width="30" height="30" as="geometry">
                        <mxRectangle width="30" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="210" value="声音ID" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;align=left;spacingLeft=6;fontStyle=0;overflow=hidden;" parent="208" vertex="1">
                    <mxGeometry x="30" width="150" height="30" as="geometry">
                        <mxRectangle width="150" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="50" value="" style="shape=tableRow;horizontal=0;startSize=0;swimlaneHead=0;swimlaneBody=0;fillColor=none;collapsible=0;dropTarget=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;top=0;left=0;right=0;bottom=0;" parent="43" vertex="1">
                    <mxGeometry y="150" width="180" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="51" value="" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;editable=1;overflow=hidden;" parent="50" vertex="1">
                    <mxGeometry width="30" height="30" as="geometry">
                        <mxRectangle width="30" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="52" value="音频地址" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;align=left;spacingLeft=6;overflow=hidden;" parent="50" vertex="1">
                    <mxGeometry x="30" width="150" height="30" as="geometry">
                        <mxRectangle width="150" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="73" value="" style="shape=tableRow;horizontal=0;startSize=0;swimlaneHead=0;swimlaneBody=0;fillColor=none;collapsible=0;dropTarget=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;top=0;left=0;right=0;bottom=0;" parent="43" vertex="1">
                    <mxGeometry y="180" width="180" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="74" value="" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;editable=1;overflow=hidden;" parent="73" vertex="1">
                    <mxGeometry width="30" height="30" as="geometry">
                        <mxRectangle width="30" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="75" value="音频来源&#xa;（0=上传1=合成）" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;align=left;spacingLeft=6;overflow=hidden;" parent="73" vertex="1">
                    <mxGeometry x="30" width="150" height="30" as="geometry">
                        <mxRectangle width="150" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="82" value="" style="shape=tableRow;horizontal=0;startSize=0;swimlaneHead=0;swimlaneBody=0;fillColor=none;collapsible=0;dropTarget=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;top=0;left=0;right=0;bottom=0;" parent="43" vertex="1">
                    <mxGeometry y="210" width="180" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="83" value="FK" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;editable=1;overflow=hidden;" parent="82" vertex="1">
                    <mxGeometry width="30" height="30" as="geometry">
                        <mxRectangle width="30" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="84" value="文案ID" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;align=left;spacingLeft=6;overflow=hidden;" parent="82" vertex="1">
                    <mxGeometry x="30" width="150" height="30" as="geometry">
                        <mxRectangle width="150" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="53" value="" style="shape=tableRow;horizontal=0;startSize=0;swimlaneHead=0;swimlaneBody=0;fillColor=none;collapsible=0;dropTarget=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;top=0;left=0;right=0;bottom=0;" parent="43" vertex="1">
                    <mxGeometry y="240" width="180" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="54" value="" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;editable=1;overflow=hidden;" parent="53" vertex="1">
                    <mxGeometry width="30" height="30" as="geometry">
                        <mxRectangle width="30" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="55" value="基本字段" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;align=left;spacingLeft=6;overflow=hidden;" parent="53" vertex="1">
                    <mxGeometry x="30" width="150" height="30" as="geometry">
                        <mxRectangle width="150" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="60" value="视频" style="shape=table;startSize=30;container=1;collapsible=1;childLayout=tableLayout;fixedRows=1;rowLines=0;fontStyle=1;align=center;resizeLast=1;" parent="1" vertex="1">
                    <mxGeometry x="600" y="-380" width="180" height="260" as="geometry"/>
                </mxCell>
                <mxCell id="61" value="" style="shape=tableRow;horizontal=0;startSize=0;swimlaneHead=0;swimlaneBody=0;fillColor=none;collapsible=0;dropTarget=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;top=0;left=0;right=0;bottom=1;" parent="60" vertex="1">
                    <mxGeometry y="30" width="180" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="62" value="PK" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;fontStyle=1;overflow=hidden;" parent="61" vertex="1">
                    <mxGeometry width="30" height="30" as="geometry">
                        <mxRectangle width="30" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="63" value="视频ID" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;align=left;spacingLeft=6;fontStyle=5;overflow=hidden;" parent="61" vertex="1">
                    <mxGeometry x="30" width="150" height="30" as="geometry">
                        <mxRectangle width="150" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="91" value="" style="shape=tableRow;horizontal=0;startSize=0;swimlaneHead=0;swimlaneBody=0;fillColor=none;collapsible=0;dropTarget=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;top=0;left=0;right=0;bottom=0;" parent="60" vertex="1">
                    <mxGeometry y="60" width="180" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="92" value="FK" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;fontStyle=0;overflow=hidden;" parent="91" vertex="1">
                    <mxGeometry width="30" height="30" as="geometry">
                        <mxRectangle width="30" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="93" value="分类ID" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;align=left;spacingLeft=6;fontStyle=0;overflow=hidden;" parent="91" vertex="1">
                    <mxGeometry x="30" width="150" height="30" as="geometry">
                        <mxRectangle width="150" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="64" value="" style="shape=tableRow;horizontal=0;startSize=0;swimlaneHead=0;swimlaneBody=0;fillColor=none;collapsible=0;dropTarget=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;top=0;left=0;right=0;bottom=0;" parent="60" vertex="1">
                    <mxGeometry y="90" width="180" height="20" as="geometry"/>
                </mxCell>
                <mxCell id="65" value="" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;editable=1;overflow=hidden;" parent="64" vertex="1">
                    <mxGeometry width="30" height="20" as="geometry">
                        <mxRectangle width="30" height="20" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="66" value="视频名称" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;align=left;spacingLeft=6;overflow=hidden;" parent="64" vertex="1">
                    <mxGeometry x="30" width="150" height="20" as="geometry">
                        <mxRectangle width="150" height="20" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="67" value="" style="shape=tableRow;horizontal=0;startSize=0;swimlaneHead=0;swimlaneBody=0;fillColor=none;collapsible=0;dropTarget=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;top=0;left=0;right=0;bottom=0;" parent="60" vertex="1">
                    <mxGeometry y="110" width="180" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="68" value="" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;editable=1;overflow=hidden;" parent="67" vertex="1">
                    <mxGeometry width="30" height="30" as="geometry">
                        <mxRectangle width="30" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="69" value="视频地址" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;align=left;spacingLeft=6;overflow=hidden;" parent="67" vertex="1">
                    <mxGeometry x="30" width="150" height="30" as="geometry">
                        <mxRectangle width="150" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="79" value="" style="shape=tableRow;horizontal=0;startSize=0;swimlaneHead=0;swimlaneBody=0;fillColor=none;collapsible=0;dropTarget=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;top=0;left=0;right=0;bottom=0;" parent="60" vertex="1">
                    <mxGeometry y="140" width="180" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="80" value="" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;editable=1;overflow=hidden;" parent="79" vertex="1">
                    <mxGeometry width="30" height="30" as="geometry">
                        <mxRectangle width="30" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="81" value="视频来源&#xa;（0=上传1=合成）" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;align=left;spacingLeft=6;overflow=hidden;" parent="79" vertex="1">
                    <mxGeometry x="30" width="150" height="30" as="geometry">
                        <mxRectangle width="150" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="211" value="" style="shape=tableRow;horizontal=0;startSize=0;swimlaneHead=0;swimlaneBody=0;fillColor=none;collapsible=0;dropTarget=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;top=0;left=0;right=0;bottom=0;" parent="60" vertex="1">
                    <mxGeometry y="170" width="180" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="212" value="FK" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;fontStyle=0;overflow=hidden;" parent="211" vertex="1">
                    <mxGeometry width="30" height="30" as="geometry">
                        <mxRectangle width="30" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="213" value="形象ID" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;align=left;spacingLeft=6;fontStyle=0;overflow=hidden;" parent="211" vertex="1">
                    <mxGeometry x="30" width="150" height="30" as="geometry">
                        <mxRectangle width="150" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="96" value="" style="shape=tableRow;horizontal=0;startSize=0;swimlaneHead=0;swimlaneBody=0;fillColor=none;collapsible=0;dropTarget=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;top=0;left=0;right=0;bottom=0;" parent="60" vertex="1">
                    <mxGeometry y="200" width="180" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="97" value="FK" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;fontStyle=0;overflow=hidden;" parent="96" vertex="1">
                    <mxGeometry width="30" height="30" as="geometry">
                        <mxRectangle width="30" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="98" value="音频ID" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;align=left;spacingLeft=6;fontStyle=0;overflow=hidden;" parent="96" vertex="1">
                    <mxGeometry x="30" width="150" height="30" as="geometry">
                        <mxRectangle width="150" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="70" value="" style="shape=tableRow;horizontal=0;startSize=0;swimlaneHead=0;swimlaneBody=0;fillColor=none;collapsible=0;dropTarget=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;top=0;left=0;right=0;bottom=0;" parent="60" vertex="1">
                    <mxGeometry y="230" width="180" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="71" value="" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;editable=1;overflow=hidden;" parent="70" vertex="1">
                    <mxGeometry width="30" height="30" as="geometry">
                        <mxRectangle width="30" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="72" value="基本字段" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;align=left;spacingLeft=6;overflow=hidden;" parent="70" vertex="1">
                    <mxGeometry x="30" width="150" height="30" as="geometry">
                        <mxRectangle width="150" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="86" value="" style="edgeStyle=entityRelationEdgeStyle;fontSize=12;html=1;endArrow=ERzeroToMany;endFill=1;" parent="1" source="3" target="19" edge="1">
                    <mxGeometry width="100" height="100" relative="1" as="geometry">
                        <mxPoint x="200" y="600" as="sourcePoint"/>
                        <mxPoint x="300" y="500" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="87" value="" style="edgeStyle=entityRelationEdgeStyle;fontSize=12;html=1;endArrow=ERzeroToMany;endFill=1;" parent="1" source="16" target="32" edge="1">
                    <mxGeometry width="100" height="100" relative="1" as="geometry">
                        <mxPoint x="420" y="435" as="sourcePoint"/>
                        <mxPoint x="520" y="335" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="88" value="" style="edgeStyle=entityRelationEdgeStyle;fontSize=12;html=1;endArrow=ERzeroToMany;endFill=1;" parent="1" source="16" target="56" edge="1">
                    <mxGeometry width="100" height="100" relative="1" as="geometry">
                        <mxPoint x="480" y="465" as="sourcePoint"/>
                        <mxPoint x="580" y="365" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="89" value="" style="edgeStyle=entityRelationEdgeStyle;fontSize=12;html=1;endArrow=ERzeroToMany;endFill=1;" parent="1" source="16" target="91" edge="1">
                    <mxGeometry width="100" height="100" relative="1" as="geometry">
                        <mxPoint x="470" y="485" as="sourcePoint"/>
                        <mxPoint x="570" y="385" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="95" value="" style="edgeStyle=entityRelationEdgeStyle;fontSize=12;html=1;endArrow=ERzeroToMany;endFill=1;" parent="1" source="29" target="82" edge="1">
                    <mxGeometry width="100" height="100" relative="1" as="geometry">
                        <mxPoint x="880" y="-105" as="sourcePoint"/>
                        <mxPoint x="980" y="-205" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="103" value="" style="edgeStyle=entityRelationEdgeStyle;fontSize=12;html=1;endArrow=ERzeroToMany;endFill=1;" parent="1" source="44" target="96" edge="1">
                    <mxGeometry width="100" height="100" relative="1" as="geometry">
                        <mxPoint x="910" y="145" as="sourcePoint"/>
                        <mxPoint x="1010" y="45" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="104" value="混剪" style="shape=table;startSize=30;container=1;collapsible=1;childLayout=tableLayout;fixedRows=1;rowLines=0;fontStyle=1;align=center;resizeLast=1;" parent="1" vertex="1">
                    <mxGeometry x="-230" y="30" width="190" height="150" as="geometry"/>
                </mxCell>
                <mxCell id="105" value="" style="shape=tableRow;horizontal=0;startSize=0;swimlaneHead=0;swimlaneBody=0;fillColor=none;collapsible=0;dropTarget=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;top=0;left=0;right=0;bottom=1;" parent="104" vertex="1">
                    <mxGeometry y="30" width="190" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="106" value="PK" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;fontStyle=1;overflow=hidden;" parent="105" vertex="1">
                    <mxGeometry width="30" height="30" as="geometry">
                        <mxRectangle width="30" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="107" value="混剪ID" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;align=left;spacingLeft=6;fontStyle=5;overflow=hidden;" parent="105" vertex="1">
                    <mxGeometry x="30" width="160" height="30" as="geometry">
                        <mxRectangle width="160" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="117" value="" style="shape=tableRow;horizontal=0;startSize=0;swimlaneHead=0;swimlaneBody=0;fillColor=none;collapsible=0;dropTarget=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;top=0;left=0;right=0;bottom=0;" parent="104" vertex="1">
                    <mxGeometry y="60" width="190" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="118" value="FK" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;fontStyle=0;overflow=hidden;" parent="117" vertex="1">
                    <mxGeometry width="30" height="30" as="geometry">
                        <mxRectangle width="30" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="119" value="项目ID" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;align=left;spacingLeft=6;fontStyle=0;overflow=hidden;" parent="117" vertex="1">
                    <mxGeometry x="30" width="160" height="30" as="geometry">
                        <mxRectangle width="160" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="108" value="" style="shape=tableRow;horizontal=0;startSize=0;swimlaneHead=0;swimlaneBody=0;fillColor=none;collapsible=0;dropTarget=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;top=0;left=0;right=0;bottom=0;" parent="104" vertex="1">
                    <mxGeometry y="90" width="190" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="109" value="" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;editable=1;overflow=hidden;" parent="108" vertex="1">
                    <mxGeometry width="30" height="30" as="geometry">
                        <mxRectangle width="30" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="110" value="规则名称" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;align=left;spacingLeft=6;overflow=hidden;" parent="108" vertex="1">
                    <mxGeometry x="30" width="160" height="30" as="geometry">
                        <mxRectangle width="160" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="111" value="" style="shape=tableRow;horizontal=0;startSize=0;swimlaneHead=0;swimlaneBody=0;fillColor=none;collapsible=0;dropTarget=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;top=0;left=0;right=0;bottom=0;" parent="104" vertex="1">
                    <mxGeometry y="120" width="190" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="112" value="" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;editable=1;overflow=hidden;" parent="111" vertex="1">
                    <mxGeometry width="30" height="30" as="geometry">
                        <mxRectangle width="30" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="113" value="基本字段" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;align=left;spacingLeft=6;overflow=hidden;" parent="111" vertex="1">
                    <mxGeometry x="30" width="160" height="30" as="geometry">
                        <mxRectangle width="160" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="120" value="" style="edgeStyle=entityRelationEdgeStyle;fontSize=12;html=1;endArrow=ERzeroToMany;endFill=1;" parent="1" source="3" target="117" edge="1">
                    <mxGeometry width="100" height="100" relative="1" as="geometry">
                        <mxPoint x="150" y="770" as="sourcePoint"/>
                        <mxPoint x="250" y="670" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="121" value="混剪规则" style="shape=table;startSize=30;container=1;collapsible=1;childLayout=tableLayout;fixedRows=1;rowLines=0;fontStyle=1;align=center;resizeLast=1;" parent="1" vertex="1">
                    <mxGeometry x="-480" y="30" width="183" height="210" as="geometry"/>
                </mxCell>
                <mxCell id="122" value="" style="shape=tableRow;horizontal=0;startSize=0;swimlaneHead=0;swimlaneBody=0;fillColor=none;collapsible=0;dropTarget=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;top=0;left=0;right=0;bottom=1;" parent="121" vertex="1">
                    <mxGeometry y="30" width="183" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="123" value="PK" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;fontStyle=1;overflow=hidden;" parent="122" vertex="1">
                    <mxGeometry width="30" height="30" as="geometry">
                        <mxRectangle width="30" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="124" value="规则ID" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;align=left;spacingLeft=6;fontStyle=5;overflow=hidden;" parent="122" vertex="1">
                    <mxGeometry x="30" width="153" height="30" as="geometry">
                        <mxRectangle width="153" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="134" value="" style="shape=tableRow;horizontal=0;startSize=0;swimlaneHead=0;swimlaneBody=0;fillColor=none;collapsible=0;dropTarget=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;top=0;left=0;right=0;bottom=0;" parent="121" vertex="1">
                    <mxGeometry y="60" width="183" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="135" value="FK" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;fontStyle=0;overflow=hidden;" parent="134" vertex="1">
                    <mxGeometry width="30" height="30" as="geometry">
                        <mxRectangle width="30" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="136" value="分类ID" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;align=left;spacingLeft=6;fontStyle=0;overflow=hidden;" parent="134" vertex="1">
                    <mxGeometry x="30" width="153" height="30" as="geometry">
                        <mxRectangle width="153" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="138" value="" style="shape=tableRow;horizontal=0;startSize=0;swimlaneHead=0;swimlaneBody=0;fillColor=none;collapsible=0;dropTarget=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;top=0;left=0;right=0;bottom=0;" parent="121" vertex="1">
                    <mxGeometry y="90" width="183" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="139" value="FK" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;fontStyle=0;overflow=hidden;" parent="138" vertex="1">
                    <mxGeometry width="30" height="30" as="geometry">
                        <mxRectangle width="30" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="140" value="混剪ID" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;align=left;spacingLeft=6;fontStyle=0;overflow=hidden;" parent="138" vertex="1">
                    <mxGeometry x="30" width="153" height="30" as="geometry">
                        <mxRectangle width="153" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="125" value="" style="shape=tableRow;horizontal=0;startSize=0;swimlaneHead=0;swimlaneBody=0;fillColor=none;collapsible=0;dropTarget=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;top=0;left=0;right=0;bottom=0;" parent="121" vertex="1">
                    <mxGeometry y="120" width="183" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="126" value="" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;editable=1;overflow=hidden;" parent="125" vertex="1">
                    <mxGeometry width="30" height="30" as="geometry">
                        <mxRectangle width="30" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="127" value="子规则类型&#xa;（1=字幕2=音频3=视频）" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;align=left;spacingLeft=6;overflow=hidden;" parent="125" vertex="1">
                    <mxGeometry x="30" width="153" height="30" as="geometry">
                        <mxRectangle width="153" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="128" value="" style="shape=tableRow;horizontal=0;startSize=0;swimlaneHead=0;swimlaneBody=0;fillColor=none;collapsible=0;dropTarget=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;top=0;left=0;right=0;bottom=0;" parent="121" vertex="1">
                    <mxGeometry y="150" width="183" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="129" value="" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;editable=1;overflow=hidden;" parent="128" vertex="1">
                    <mxGeometry width="30" height="30" as="geometry">
                        <mxRectangle width="30" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="130" value="开始时间" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;align=left;spacingLeft=6;overflow=hidden;" parent="128" vertex="1">
                    <mxGeometry x="30" width="153" height="30" as="geometry">
                        <mxRectangle width="153" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="131" value="" style="shape=tableRow;horizontal=0;startSize=0;swimlaneHead=0;swimlaneBody=0;fillColor=none;collapsible=0;dropTarget=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;top=0;left=0;right=0;bottom=0;" parent="121" vertex="1">
                    <mxGeometry y="180" width="183" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="132" value="" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;editable=1;overflow=hidden;" parent="131" vertex="1">
                    <mxGeometry width="30" height="30" as="geometry">
                        <mxRectangle width="30" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="133" value="结束时间" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;align=left;spacingLeft=6;overflow=hidden;" parent="131" vertex="1">
                    <mxGeometry x="30" width="153" height="30" as="geometry">
                        <mxRectangle width="153" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="141" value="" style="edgeStyle=entityRelationEdgeStyle;fontSize=12;html=1;endArrow=ERzeroToMany;endFill=1;" parent="1" source="105" target="138" edge="1">
                    <mxGeometry width="100" height="100" relative="1" as="geometry">
                        <mxPoint x="500" y="1070" as="sourcePoint"/>
                        <mxPoint x="600" y="970" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="143" value="直播" style="shape=table;startSize=30;container=1;collapsible=1;childLayout=tableLayout;fixedRows=1;rowLines=0;fontStyle=1;align=center;resizeLast=1;" parent="1" vertex="1">
                    <mxGeometry x="-230" y="220" width="200" height="240" as="geometry"/>
                </mxCell>
                <mxCell id="144" value="" style="shape=tableRow;horizontal=0;startSize=0;swimlaneHead=0;swimlaneBody=0;fillColor=none;collapsible=0;dropTarget=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;top=0;left=0;right=0;bottom=1;" parent="143" vertex="1">
                    <mxGeometry y="30" width="200" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="145" value="PK" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;fontStyle=1;overflow=hidden;" parent="144" vertex="1">
                    <mxGeometry width="30" height="30" as="geometry">
                        <mxRectangle width="30" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="146" value="直播ID" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;align=left;spacingLeft=6;fontStyle=5;overflow=hidden;" parent="144" vertex="1">
                    <mxGeometry x="30" width="170" height="30" as="geometry">
                        <mxRectangle width="170" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="176" value="" style="shape=tableRow;horizontal=0;startSize=0;swimlaneHead=0;swimlaneBody=0;fillColor=none;collapsible=0;dropTarget=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;top=0;left=0;right=0;bottom=0;" parent="143" vertex="1">
                    <mxGeometry y="60" width="200" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="177" value="FK" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;fontStyle=0;overflow=hidden;" parent="176" vertex="1">
                    <mxGeometry width="30" height="30" as="geometry">
                        <mxRectangle width="30" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="178" value="项目ID" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;align=left;spacingLeft=6;fontStyle=0;overflow=hidden;" parent="176" vertex="1">
                    <mxGeometry x="30" width="170" height="30" as="geometry">
                        <mxRectangle width="170" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="502" value="" style="shape=tableRow;horizontal=0;startSize=0;swimlaneHead=0;swimlaneBody=0;fillColor=none;collapsible=0;dropTarget=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;top=0;left=0;right=0;bottom=0;" parent="143" vertex="1">
                    <mxGeometry y="90" width="200" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="503" value="" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;editable=1;overflow=hidden;" parent="502" vertex="1">
                    <mxGeometry width="30" height="30" as="geometry">
                        <mxRectangle width="30" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="504" value="直播名称" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;align=left;spacingLeft=6;overflow=hidden;" parent="502" vertex="1">
                    <mxGeometry x="30" width="170" height="30" as="geometry">
                        <mxRectangle width="170" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="147" value="" style="shape=tableRow;horizontal=0;startSize=0;swimlaneHead=0;swimlaneBody=0;fillColor=none;collapsible=0;dropTarget=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;top=0;left=0;right=0;bottom=0;" parent="143" vertex="1">
                    <mxGeometry y="120" width="200" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="148" value="" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;editable=1;overflow=hidden;" parent="147" vertex="1">
                    <mxGeometry width="30" height="30" as="geometry">
                        <mxRectangle width="30" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="149" value="直播类型&#xa;（0=实景无人播，1=数字人直播）" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;align=left;spacingLeft=6;overflow=hidden;" parent="147" vertex="1">
                    <mxGeometry x="30" width="170" height="30" as="geometry">
                        <mxRectangle width="170" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="150" value="" style="shape=tableRow;horizontal=0;startSize=0;swimlaneHead=0;swimlaneBody=0;fillColor=none;collapsible=0;dropTarget=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;top=0;left=0;right=0;bottom=0;" parent="143" vertex="1">
                    <mxGeometry y="150" width="200" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="151" value="" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;editable=1;overflow=hidden;" parent="150" vertex="1">
                    <mxGeometry width="30" height="30" as="geometry">
                        <mxRectangle width="30" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="152" value="场控id" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;align=left;spacingLeft=6;overflow=hidden;" parent="150" vertex="1">
                    <mxGeometry x="30" width="170" height="30" as="geometry">
                        <mxRectangle width="170" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="469" value="" style="shape=tableRow;horizontal=0;startSize=0;swimlaneHead=0;swimlaneBody=0;fillColor=none;collapsible=0;dropTarget=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;top=0;left=0;right=0;bottom=0;" parent="143" vertex="1">
                    <mxGeometry y="180" width="200" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="470" value="" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;editable=1;overflow=hidden;" parent="469" vertex="1">
                    <mxGeometry width="30" height="30" as="geometry">
                        <mxRectangle width="30" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="471" value="产品ids" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;align=left;spacingLeft=6;overflow=hidden;" parent="469" vertex="1">
                    <mxGeometry x="30" width="170" height="30" as="geometry">
                        <mxRectangle width="170" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="153" value="" style="shape=tableRow;horizontal=0;startSize=0;swimlaneHead=0;swimlaneBody=0;fillColor=none;collapsible=0;dropTarget=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;top=0;left=0;right=0;bottom=0;" parent="143" vertex="1">
                    <mxGeometry y="210" width="200" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="154" value="" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;editable=1;overflow=hidden;" parent="153" vertex="1">
                    <mxGeometry width="30" height="30" as="geometry">
                        <mxRectangle width="30" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="155" value="基本字段" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;align=left;spacingLeft=6;overflow=hidden;" parent="153" vertex="1">
                    <mxGeometry x="30" width="170" height="30" as="geometry">
                        <mxRectangle width="170" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="179" value="" style="edgeStyle=entityRelationEdgeStyle;fontSize=12;html=1;endArrow=ERzeroToMany;endFill=1;" parent="1" source="3" target="176" edge="1">
                    <mxGeometry width="100" height="100" relative="1" as="geometry">
                        <mxPoint x="-160" y="570" as="sourcePoint"/>
                        <mxPoint x="-60" y="470" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="195" value="形象" style="shape=table;startSize=30;container=1;collapsible=1;childLayout=tableLayout;fixedRows=1;rowLines=0;fontStyle=1;align=center;resizeLast=1;" parent="1" vertex="1">
                    <mxGeometry x="850" y="-400" width="300" height="210" as="geometry"/>
                </mxCell>
                <mxCell id="196" value="" style="shape=tableRow;horizontal=0;startSize=0;swimlaneHead=0;swimlaneBody=0;fillColor=none;collapsible=0;dropTarget=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;top=0;left=0;right=0;bottom=1;" parent="195" vertex="1">
                    <mxGeometry y="30" width="300" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="197" value="PK" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;fontStyle=1;overflow=hidden;" parent="196" vertex="1">
                    <mxGeometry width="30" height="30" as="geometry">
                        <mxRectangle width="30" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="198" value="形象ID" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;align=left;spacingLeft=6;fontStyle=5;overflow=hidden;" parent="196" vertex="1">
                    <mxGeometry x="30" width="270" height="30" as="geometry">
                        <mxRectangle width="270" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="199" value="" style="shape=tableRow;horizontal=0;startSize=0;swimlaneHead=0;swimlaneBody=0;fillColor=none;collapsible=0;dropTarget=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;top=0;left=0;right=0;bottom=0;" parent="195" vertex="1">
                    <mxGeometry y="60" width="300" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="200" value="" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;editable=1;overflow=hidden;" parent="199" vertex="1">
                    <mxGeometry width="30" height="30" as="geometry">
                        <mxRectangle width="30" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="201" value="形象名称" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;align=left;spacingLeft=6;overflow=hidden;" parent="199" vertex="1">
                    <mxGeometry x="30" width="270" height="30" as="geometry">
                        <mxRectangle width="270" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="202" value="" style="shape=tableRow;horizontal=0;startSize=0;swimlaneHead=0;swimlaneBody=0;fillColor=none;collapsible=0;dropTarget=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;top=0;left=0;right=0;bottom=0;" parent="195" vertex="1">
                    <mxGeometry y="90" width="300" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="203" value="" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;editable=1;overflow=hidden;" parent="202" vertex="1">
                    <mxGeometry width="30" height="30" as="geometry">
                        <mxRectangle width="30" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="204" value="形象代号" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;align=left;spacingLeft=6;overflow=hidden;" parent="202" vertex="1">
                    <mxGeometry x="30" width="270" height="30" as="geometry">
                        <mxRectangle width="270" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="205" value="" style="shape=tableRow;horizontal=0;startSize=0;swimlaneHead=0;swimlaneBody=0;fillColor=none;collapsible=0;dropTarget=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;top=0;left=0;right=0;bottom=0;" parent="195" vertex="1">
                    <mxGeometry y="120" width="300" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="206" value="" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;editable=1;overflow=hidden;" parent="205" vertex="1">
                    <mxGeometry width="30" height="30" as="geometry">
                        <mxRectangle width="30" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="207" value="形象状态&#xa;（0=待训练，1=队列中，2=完成，3=失败）" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;align=left;spacingLeft=6;overflow=hidden;" parent="205" vertex="1">
                    <mxGeometry x="30" width="270" height="30" as="geometry">
                        <mxRectangle width="270" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="733" value="" style="shape=tableRow;horizontal=0;startSize=0;swimlaneHead=0;swimlaneBody=0;fillColor=none;collapsible=0;dropTarget=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;top=0;left=0;right=0;bottom=0;" parent="195" vertex="1">
                    <mxGeometry y="150" width="300" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="734" value="" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;editable=1;overflow=hidden;" parent="733" vertex="1">
                    <mxGeometry width="30" height="30" as="geometry">
                        <mxRectangle width="30" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="735" value="形象存储路径" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;align=left;spacingLeft=6;overflow=hidden;" parent="733" vertex="1">
                    <mxGeometry x="30" width="270" height="30" as="geometry">
                        <mxRectangle width="270" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="220" value="" style="shape=tableRow;horizontal=0;startSize=0;swimlaneHead=0;swimlaneBody=0;fillColor=none;collapsible=0;dropTarget=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;top=0;left=0;right=0;bottom=0;" parent="195" vertex="1">
                    <mxGeometry y="180" width="300" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="221" value="" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;editable=1;overflow=hidden;" parent="220" vertex="1">
                    <mxGeometry width="30" height="30" as="geometry">
                        <mxRectangle width="30" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="222" value="基本字段" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;align=left;spacingLeft=6;overflow=hidden;" parent="220" vertex="1">
                    <mxGeometry x="30" width="270" height="30" as="geometry">
                        <mxRectangle width="270" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="216" value="" style="edgeStyle=entityRelationEdgeStyle;fontSize=12;html=1;endArrow=ERzeroToMany;endFill=1;" parent="1" source="196" target="211" edge="1">
                    <mxGeometry width="100" height="100" relative="1" as="geometry">
                        <mxPoint x="910" y="-190" as="sourcePoint"/>
                        <mxPoint x="1010" y="-290" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="223" value="场控" style="shape=table;startSize=30;container=1;collapsible=1;childLayout=tableLayout;fixedRows=1;rowLines=0;fontStyle=1;align=center;resizeLast=1;" parent="1" vertex="1">
                    <mxGeometry x="-230" y="490" width="200" height="210" as="geometry"/>
                </mxCell>
                <mxCell id="224" value="" style="shape=tableRow;horizontal=0;startSize=0;swimlaneHead=0;swimlaneBody=0;fillColor=none;collapsible=0;dropTarget=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;top=0;left=0;right=0;bottom=1;" parent="223" vertex="1">
                    <mxGeometry y="30" width="200" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="225" value="PK" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;fontStyle=1;overflow=hidden;" parent="224" vertex="1">
                    <mxGeometry width="30" height="30" as="geometry">
                        <mxRectangle width="30" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="226" value="场控id" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;align=left;spacingLeft=6;fontStyle=5;overflow=hidden;" parent="224" vertex="1">
                    <mxGeometry x="30" width="170" height="30" as="geometry">
                        <mxRectangle width="170" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="227" value="" style="shape=tableRow;horizontal=0;startSize=0;swimlaneHead=0;swimlaneBody=0;fillColor=none;collapsible=0;dropTarget=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;top=0;left=0;right=0;bottom=0;" parent="223" vertex="1">
                    <mxGeometry y="60" width="200" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="228" value="" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;editable=1;overflow=hidden;" parent="227" vertex="1">
                    <mxGeometry width="30" height="30" as="geometry">
                        <mxRectangle width="30" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="229" value="项目id" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;align=left;spacingLeft=6;overflow=hidden;" parent="227" vertex="1">
                    <mxGeometry x="30" width="170" height="30" as="geometry">
                        <mxRectangle width="170" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="430" value="" style="shape=tableRow;horizontal=0;startSize=0;swimlaneHead=0;swimlaneBody=0;fillColor=none;collapsible=0;dropTarget=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;top=0;left=0;right=0;bottom=0;" parent="223" vertex="1">
                    <mxGeometry y="90" width="200" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="431" value="" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;editable=1;overflow=hidden;" parent="430" vertex="1">
                    <mxGeometry width="30" height="30" as="geometry">
                        <mxRectangle width="30" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="432" value="场控名称" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;align=left;spacingLeft=6;overflow=hidden;" parent="430" vertex="1">
                    <mxGeometry x="30" width="170" height="30" as="geometry">
                        <mxRectangle width="170" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="230" value="" style="shape=tableRow;horizontal=0;startSize=0;swimlaneHead=0;swimlaneBody=0;fillColor=none;collapsible=0;dropTarget=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;top=0;left=0;right=0;bottom=0;" parent="223" vertex="1">
                    <mxGeometry y="120" width="200" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="231" value="" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;editable=1;overflow=hidden;" parent="230" vertex="1">
                    <mxGeometry width="30" height="30" as="geometry">
                        <mxRectangle width="30" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="232" value="场控互动分类ids" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;align=left;spacingLeft=6;overflow=hidden;" parent="230" vertex="1">
                    <mxGeometry x="30" width="170" height="30" as="geometry">
                        <mxRectangle width="170" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="233" value="" style="shape=tableRow;horizontal=0;startSize=0;swimlaneHead=0;swimlaneBody=0;fillColor=none;collapsible=0;dropTarget=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;top=0;left=0;right=0;bottom=0;" parent="223" vertex="1">
                    <mxGeometry y="150" width="200" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="234" value="" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;editable=1;overflow=hidden;" parent="233" vertex="1">
                    <mxGeometry width="30" height="30" as="geometry">
                        <mxRectangle width="30" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="235" value="场控问答分类ids" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;align=left;spacingLeft=6;overflow=hidden;" parent="233" vertex="1">
                    <mxGeometry x="30" width="170" height="30" as="geometry">
                        <mxRectangle width="170" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="481" value="" style="shape=tableRow;horizontal=0;startSize=0;swimlaneHead=0;swimlaneBody=0;fillColor=none;collapsible=0;dropTarget=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;top=0;left=0;right=0;bottom=0;" parent="223" vertex="1">
                    <mxGeometry y="180" width="200" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="482" value="" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;editable=1;overflow=hidden;" parent="481" vertex="1">
                    <mxGeometry width="30" height="30" as="geometry">
                        <mxRectangle width="30" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="483" value="基本字段" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;align=left;spacingLeft=6;overflow=hidden;" parent="481" vertex="1">
                    <mxGeometry x="30" width="170" height="30" as="geometry">
                        <mxRectangle width="170" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="249" value="" style="edgeStyle=entityRelationEdgeStyle;fontSize=12;html=1;endArrow=ERzeroToMany;endFill=1;" parent="1" source="3" target="227" edge="1">
                    <mxGeometry width="100" height="100" relative="1" as="geometry">
                        <mxPoint x="-560" y="520" as="sourcePoint"/>
                        <mxPoint x="-460" y="420" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="250" value="产品" style="shape=table;startSize=30;container=1;collapsible=1;childLayout=tableLayout;fixedRows=1;rowLines=0;fontStyle=1;align=center;resizeLast=1;" parent="1" vertex="1">
                    <mxGeometry x="-230" y="740" width="200" height="210" as="geometry"/>
                </mxCell>
                <mxCell id="251" value="" style="shape=tableRow;horizontal=0;startSize=0;swimlaneHead=0;swimlaneBody=0;fillColor=none;collapsible=0;dropTarget=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;top=0;left=0;right=0;bottom=1;" parent="250" vertex="1">
                    <mxGeometry y="30" width="200" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="252" value="PK" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;fontStyle=1;overflow=hidden;" parent="251" vertex="1">
                    <mxGeometry width="30" height="30" as="geometry">
                        <mxRectangle width="30" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="253" value="产品ID" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;align=left;spacingLeft=6;fontStyle=5;overflow=hidden;" parent="251" vertex="1">
                    <mxGeometry x="30" width="170" height="30" as="geometry">
                        <mxRectangle width="170" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="424" value="" style="shape=tableRow;horizontal=0;startSize=0;swimlaneHead=0;swimlaneBody=0;fillColor=none;collapsible=0;dropTarget=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;top=0;left=0;right=0;bottom=0;" parent="250" vertex="1">
                    <mxGeometry y="60" width="200" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="425" value="" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;editable=1;overflow=hidden;" parent="424" vertex="1">
                    <mxGeometry width="30" height="30" as="geometry">
                        <mxRectangle width="30" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="426" value="项目id" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;align=left;spacingLeft=6;overflow=hidden;" parent="424" vertex="1">
                    <mxGeometry x="30" width="170" height="30" as="geometry">
                        <mxRectangle width="170" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="257" value="" style="shape=tableRow;horizontal=0;startSize=0;swimlaneHead=0;swimlaneBody=0;fillColor=none;collapsible=0;dropTarget=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;top=0;left=0;right=0;bottom=0;" parent="250" vertex="1">
                    <mxGeometry y="90" width="200" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="258" value="" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;editable=1;overflow=hidden;" parent="257" vertex="1">
                    <mxGeometry width="30" height="30" as="geometry">
                        <mxRectangle width="30" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="259" value="产品名称" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;align=left;spacingLeft=6;overflow=hidden;" parent="257" vertex="1">
                    <mxGeometry x="30" width="170" height="30" as="geometry">
                        <mxRectangle width="170" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="279" value="" style="shape=tableRow;horizontal=0;startSize=0;swimlaneHead=0;swimlaneBody=0;fillColor=none;collapsible=0;dropTarget=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;top=0;left=0;right=0;bottom=0;" parent="250" vertex="1">
                    <mxGeometry y="120" width="200" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="280" value="" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;editable=1;overflow=hidden;" parent="279" vertex="1">
                    <mxGeometry width="30" height="30" as="geometry">
                        <mxRectangle width="30" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="281" value="产品互动分类ids" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;align=left;spacingLeft=6;overflow=hidden;" parent="279" vertex="1">
                    <mxGeometry x="30" width="170" height="30" as="geometry">
                        <mxRectangle width="170" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="285" value="" style="shape=tableRow;horizontal=0;startSize=0;swimlaneHead=0;swimlaneBody=0;fillColor=none;collapsible=0;dropTarget=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;top=0;left=0;right=0;bottom=0;" parent="250" vertex="1">
                    <mxGeometry y="150" width="200" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="286" value="" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;editable=1;overflow=hidden;" parent="285" vertex="1">
                    <mxGeometry width="30" height="30" as="geometry">
                        <mxRectangle width="30" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="287" value="产品问答分类ids" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;align=left;spacingLeft=6;overflow=hidden;" parent="285" vertex="1">
                    <mxGeometry x="30" width="170" height="30" as="geometry">
                        <mxRectangle width="170" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="484" value="" style="shape=tableRow;horizontal=0;startSize=0;swimlaneHead=0;swimlaneBody=0;fillColor=none;collapsible=0;dropTarget=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;top=0;left=0;right=0;bottom=0;" parent="250" vertex="1">
                    <mxGeometry y="180" width="200" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="485" value="" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;editable=1;overflow=hidden;" parent="484" vertex="1">
                    <mxGeometry width="30" height="30" as="geometry">
                        <mxRectangle width="30" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="486" value="基本字段" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;align=left;spacingLeft=6;overflow=hidden;" parent="484" vertex="1">
                    <mxGeometry x="30" width="170" height="30" as="geometry">
                        <mxRectangle width="170" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="301" value="关键词" style="shape=table;startSize=30;container=1;collapsible=1;childLayout=tableLayout;fixedRows=1;rowLines=0;fontStyle=1;align=center;resizeLast=1;" parent="1" vertex="1">
                    <mxGeometry x="600" y="350" width="180" height="150" as="geometry"/>
                </mxCell>
                <mxCell id="302" value="" style="shape=tableRow;horizontal=0;startSize=0;swimlaneHead=0;swimlaneBody=0;fillColor=none;collapsible=0;dropTarget=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;top=0;left=0;right=0;bottom=1;" parent="301" vertex="1">
                    <mxGeometry y="30" width="180" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="303" value="PK" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;fontStyle=1;overflow=hidden;" parent="302" vertex="1">
                    <mxGeometry width="30" height="30" as="geometry">
                        <mxRectangle width="30" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="304" value="关键词ID" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;align=left;spacingLeft=6;fontStyle=5;overflow=hidden;" parent="302" vertex="1">
                    <mxGeometry x="30" width="150" height="30" as="geometry">
                        <mxRectangle width="150" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="317" value="" style="shape=tableRow;horizontal=0;startSize=0;swimlaneHead=0;swimlaneBody=0;fillColor=none;collapsible=0;dropTarget=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;top=0;left=0;right=0;bottom=0;" parent="301" vertex="1">
                    <mxGeometry y="60" width="180" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="318" value="" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;editable=1;overflow=hidden;" parent="317" vertex="1">
                    <mxGeometry width="30" height="30" as="geometry">
                        <mxRectangle width="30" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="319" value="分类ID" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;align=left;spacingLeft=6;overflow=hidden;" parent="317" vertex="1">
                    <mxGeometry x="30" width="150" height="30" as="geometry">
                        <mxRectangle width="150" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="305" value="" style="shape=tableRow;horizontal=0;startSize=0;swimlaneHead=0;swimlaneBody=0;fillColor=none;collapsible=0;dropTarget=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;top=0;left=0;right=0;bottom=0;" parent="301" vertex="1">
                    <mxGeometry y="90" width="180" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="306" value="" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;editable=1;overflow=hidden;" parent="305" vertex="1">
                    <mxGeometry width="30" height="30" as="geometry">
                        <mxRectangle width="30" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="307" value="关键词内容" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;align=left;spacingLeft=6;overflow=hidden;" parent="305" vertex="1">
                    <mxGeometry x="30" width="150" height="30" as="geometry">
                        <mxRectangle width="150" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="314" value="" style="shape=tableRow;horizontal=0;startSize=0;swimlaneHead=0;swimlaneBody=0;fillColor=none;collapsible=0;dropTarget=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;top=0;left=0;right=0;bottom=0;" parent="301" vertex="1">
                    <mxGeometry y="120" width="180" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="315" value="" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;editable=1;overflow=hidden;" parent="314" vertex="1">
                    <mxGeometry width="30" height="30" as="geometry">
                        <mxRectangle width="30" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="316" value="基本字段（可无需备注）" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;align=left;spacingLeft=6;overflow=hidden;" parent="314" vertex="1">
                    <mxGeometry x="30" width="150" height="30" as="geometry">
                        <mxRectangle width="150" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="320" value="" style="edgeStyle=entityRelationEdgeStyle;fontSize=12;html=1;endArrow=ERzeroToMany;endFill=1;" parent="1" source="16" target="302" edge="1">
                    <mxGeometry width="100" height="100" relative="1" as="geometry">
                        <mxPoint x="460" y="420" as="sourcePoint"/>
                        <mxPoint x="560" y="320" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="330" value="" style="edgeStyle=entityRelationEdgeStyle;fontSize=12;html=1;endArrow=ERzeroToMany;endFill=1;" parent="1" source="3" target="424" edge="1">
                    <mxGeometry width="100" height="100" relative="1" as="geometry">
                        <mxPoint x="-420" y="610" as="sourcePoint"/>
                        <mxPoint x="260" y="735" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="333" value="" style="edgeStyle=entityRelationEdgeStyle;fontSize=12;html=1;endArrow=ERoneToMany;" parent="1" source="150" target="224" edge="1">
                    <mxGeometry width="100" height="100" relative="1" as="geometry">
                        <mxPoint x="-110" y="750" as="sourcePoint"/>
                        <mxPoint x="-10" y="650" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="465" value="" style="edgeStyle=entityRelationEdgeStyle;fontSize=12;html=1;endArrow=ERoneToMany;" parent="1" source="469" target="251" edge="1">
                    <mxGeometry width="100" height="100" relative="1" as="geometry">
                        <mxPoint x="-380" y="585" as="sourcePoint"/>
                        <mxPoint x="-90" y="780" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="sBk4UZyAnzUuyrO-kaQZ-526" value="智能主播" style="shape=table;startSize=30;container=1;collapsible=1;childLayout=tableLayout;fixedRows=1;rowLines=0;fontStyle=1;align=center;resizeLast=1;" parent="1" vertex="1">
                    <mxGeometry x="600" y="516" width="180" height="184" as="geometry"/>
                </mxCell>
                <mxCell id="sBk4UZyAnzUuyrO-kaQZ-527" value="" style="shape=tableRow;horizontal=0;startSize=0;swimlaneHead=0;swimlaneBody=0;fillColor=none;collapsible=0;dropTarget=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;top=0;left=0;right=0;bottom=1;" parent="sBk4UZyAnzUuyrO-kaQZ-526" vertex="1">
                    <mxGeometry y="30" width="180" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="sBk4UZyAnzUuyrO-kaQZ-528" value="PK" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;fontStyle=1;overflow=hidden;" parent="sBk4UZyAnzUuyrO-kaQZ-527" vertex="1">
                    <mxGeometry width="30" height="30" as="geometry">
                        <mxRectangle width="30" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="sBk4UZyAnzUuyrO-kaQZ-529" value="智能主播ID" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;align=left;spacingLeft=6;fontStyle=5;overflow=hidden;" parent="sBk4UZyAnzUuyrO-kaQZ-527" vertex="1">
                    <mxGeometry x="30" width="150" height="30" as="geometry">
                        <mxRectangle width="150" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="sBk4UZyAnzUuyrO-kaQZ-530" value="" style="shape=tableRow;horizontal=0;startSize=0;swimlaneHead=0;swimlaneBody=0;fillColor=none;collapsible=0;dropTarget=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;top=0;left=0;right=0;bottom=0;" parent="sBk4UZyAnzUuyrO-kaQZ-526" vertex="1">
                    <mxGeometry y="60" width="180" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="sBk4UZyAnzUuyrO-kaQZ-531" value="" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;fontStyle=0;overflow=hidden;" parent="sBk4UZyAnzUuyrO-kaQZ-530" vertex="1">
                    <mxGeometry width="30" height="30" as="geometry">
                        <mxRectangle width="30" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="sBk4UZyAnzUuyrO-kaQZ-532" value="项目Id" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;align=left;spacingLeft=6;fontStyle=0;overflow=hidden;" parent="sBk4UZyAnzUuyrO-kaQZ-530" vertex="1">
                    <mxGeometry x="30" width="150" height="30" as="geometry">
                        <mxRectangle width="150" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="sBk4UZyAnzUuyrO-kaQZ-533" value="" style="shape=tableRow;horizontal=0;startSize=0;swimlaneHead=0;swimlaneBody=0;fillColor=none;collapsible=0;dropTarget=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;top=0;left=0;right=0;bottom=0;" parent="sBk4UZyAnzUuyrO-kaQZ-526" vertex="1">
                    <mxGeometry y="90" width="180" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="sBk4UZyAnzUuyrO-kaQZ-534" value="" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;editable=1;overflow=hidden;" parent="sBk4UZyAnzUuyrO-kaQZ-533" vertex="1">
                    <mxGeometry width="30" height="30" as="geometry">
                        <mxRectangle width="30" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="sBk4UZyAnzUuyrO-kaQZ-535" value="智能主播名称" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;align=left;spacingLeft=6;overflow=hidden;" parent="sBk4UZyAnzUuyrO-kaQZ-533" vertex="1">
                    <mxGeometry x="30" width="150" height="30" as="geometry">
                        <mxRectangle width="150" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="sBk4UZyAnzUuyrO-kaQZ-536" value="" style="shape=tableRow;horizontal=0;startSize=0;swimlaneHead=0;swimlaneBody=0;fillColor=none;collapsible=0;dropTarget=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;top=0;left=0;right=0;bottom=0;" parent="sBk4UZyAnzUuyrO-kaQZ-526" vertex="1">
                    <mxGeometry y="120" width="180" height="34" as="geometry"/>
                </mxCell>
                <mxCell id="sBk4UZyAnzUuyrO-kaQZ-537" value="" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;editable=1;overflow=hidden;" parent="sBk4UZyAnzUuyrO-kaQZ-536" vertex="1">
                    <mxGeometry width="30" height="34" as="geometry">
                        <mxRectangle width="30" height="34" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="sBk4UZyAnzUuyrO-kaQZ-538" value="智能主播知识库" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;align=left;spacingLeft=6;overflow=hidden;" parent="sBk4UZyAnzUuyrO-kaQZ-536" vertex="1">
                    <mxGeometry x="30" width="150" height="34" as="geometry">
                        <mxRectangle width="150" height="34" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="sBk4UZyAnzUuyrO-kaQZ-539" value="" style="shape=tableRow;horizontal=0;startSize=0;swimlaneHead=0;swimlaneBody=0;fillColor=none;collapsible=0;dropTarget=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;top=0;left=0;right=0;bottom=0;" parent="sBk4UZyAnzUuyrO-kaQZ-526" vertex="1">
                    <mxGeometry y="154" width="180" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="sBk4UZyAnzUuyrO-kaQZ-540" value="" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;editable=1;overflow=hidden;" parent="sBk4UZyAnzUuyrO-kaQZ-539" vertex="1">
                    <mxGeometry width="30" height="30" as="geometry">
                        <mxRectangle width="30" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="sBk4UZyAnzUuyrO-kaQZ-541" value="基本字段" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;align=left;spacingLeft=6;overflow=hidden;" parent="sBk4UZyAnzUuyrO-kaQZ-539" vertex="1">
                    <mxGeometry x="30" width="150" height="30" as="geometry">
                        <mxRectangle width="150" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="sBk4UZyAnzUuyrO-kaQZ-598" value="机器码" style="shape=table;startSize=30;container=1;collapsible=1;childLayout=tableLayout;fixedRows=1;rowLines=0;fontStyle=1;align=center;resizeLast=1;" parent="1" vertex="1">
                    <mxGeometry x="600" y="810" width="200" height="154" as="geometry"/>
                </mxCell>
                <mxCell id="sBk4UZyAnzUuyrO-kaQZ-599" value="" style="shape=tableRow;horizontal=0;startSize=0;swimlaneHead=0;swimlaneBody=0;fillColor=none;collapsible=0;dropTarget=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;top=0;left=0;right=0;bottom=1;" parent="sBk4UZyAnzUuyrO-kaQZ-598" vertex="1">
                    <mxGeometry y="30" width="200" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="sBk4UZyAnzUuyrO-kaQZ-600" value="PK" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;fontStyle=1;overflow=hidden;" parent="sBk4UZyAnzUuyrO-kaQZ-599" vertex="1">
                    <mxGeometry width="30" height="30" as="geometry">
                        <mxRectangle width="30" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="sBk4UZyAnzUuyrO-kaQZ-601" value="机器码ID" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;align=left;spacingLeft=6;fontStyle=5;overflow=hidden;" parent="sBk4UZyAnzUuyrO-kaQZ-599" vertex="1">
                    <mxGeometry x="30" width="170" height="30" as="geometry">
                        <mxRectangle width="170" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="sBk4UZyAnzUuyrO-kaQZ-602" value="" style="shape=tableRow;horizontal=0;startSize=0;swimlaneHead=0;swimlaneBody=0;fillColor=none;collapsible=0;dropTarget=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;top=0;left=0;right=0;bottom=0;" parent="sBk4UZyAnzUuyrO-kaQZ-598" vertex="1">
                    <mxGeometry y="60" width="200" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="sBk4UZyAnzUuyrO-kaQZ-603" value="" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;editable=1;overflow=hidden;" parent="sBk4UZyAnzUuyrO-kaQZ-602" vertex="1">
                    <mxGeometry width="30" height="30" as="geometry">
                        <mxRectangle width="30" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="sBk4UZyAnzUuyrO-kaQZ-604" value="机器码名称" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;align=left;spacingLeft=6;overflow=hidden;" parent="sBk4UZyAnzUuyrO-kaQZ-602" vertex="1">
                    <mxGeometry x="30" width="170" height="30" as="geometry">
                        <mxRectangle width="170" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="sBk4UZyAnzUuyrO-kaQZ-605" value="" style="shape=tableRow;horizontal=0;startSize=0;swimlaneHead=0;swimlaneBody=0;fillColor=none;collapsible=0;dropTarget=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;top=0;left=0;right=0;bottom=0;" parent="sBk4UZyAnzUuyrO-kaQZ-598" vertex="1">
                    <mxGeometry y="90" width="200" height="34" as="geometry"/>
                </mxCell>
                <mxCell id="sBk4UZyAnzUuyrO-kaQZ-606" value="" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;editable=1;overflow=hidden;" parent="sBk4UZyAnzUuyrO-kaQZ-605" vertex="1">
                    <mxGeometry width="30" height="34" as="geometry">
                        <mxRectangle width="30" height="34" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="sBk4UZyAnzUuyrO-kaQZ-607" value="机器码类型（0=停用 1=正常）" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;align=left;spacingLeft=6;overflow=hidden;" parent="sBk4UZyAnzUuyrO-kaQZ-605" vertex="1">
                    <mxGeometry x="30" width="170" height="34" as="geometry">
                        <mxRectangle width="170" height="34" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="sBk4UZyAnzUuyrO-kaQZ-608" value="" style="shape=tableRow;horizontal=0;startSize=0;swimlaneHead=0;swimlaneBody=0;fillColor=none;collapsible=0;dropTarget=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;top=0;left=0;right=0;bottom=0;" parent="sBk4UZyAnzUuyrO-kaQZ-598" vertex="1">
                    <mxGeometry y="124" width="200" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="sBk4UZyAnzUuyrO-kaQZ-609" value="" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;editable=1;overflow=hidden;" parent="sBk4UZyAnzUuyrO-kaQZ-608" vertex="1">
                    <mxGeometry width="30" height="30" as="geometry">
                        <mxRectangle width="30" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="sBk4UZyAnzUuyrO-kaQZ-610" value="基本字段" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;align=left;spacingLeft=6;overflow=hidden;" parent="sBk4UZyAnzUuyrO-kaQZ-608" vertex="1">
                    <mxGeometry x="30" width="170" height="30" as="geometry">
                        <mxRectangle width="170" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="sBk4UZyAnzUuyrO-kaQZ-611" value="算力点用户" style="shape=table;startSize=30;container=1;collapsible=1;childLayout=tableLayout;fixedRows=1;rowLines=0;fontStyle=1;align=center;resizeLast=1;" parent="1" vertex="1">
                    <mxGeometry x="820" y="810" width="200" height="154" as="geometry"/>
                </mxCell>
                <mxCell id="sBk4UZyAnzUuyrO-kaQZ-612" value="" style="shape=tableRow;horizontal=0;startSize=0;swimlaneHead=0;swimlaneBody=0;fillColor=none;collapsible=0;dropTarget=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;top=0;left=0;right=0;bottom=1;" parent="sBk4UZyAnzUuyrO-kaQZ-611" vertex="1">
                    <mxGeometry y="30" width="200" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="sBk4UZyAnzUuyrO-kaQZ-613" value="PK" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;fontStyle=1;overflow=hidden;" parent="sBk4UZyAnzUuyrO-kaQZ-612" vertex="1">
                    <mxGeometry width="30" height="30" as="geometry">
                        <mxRectangle width="30" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="sBk4UZyAnzUuyrO-kaQZ-614" value="算力用户ID" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;align=left;spacingLeft=6;fontStyle=5;overflow=hidden;" parent="sBk4UZyAnzUuyrO-kaQZ-612" vertex="1">
                    <mxGeometry x="30" width="170" height="30" as="geometry">
                        <mxRectangle width="170" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="sBk4UZyAnzUuyrO-kaQZ-615" value="" style="shape=tableRow;horizontal=0;startSize=0;swimlaneHead=0;swimlaneBody=0;fillColor=none;collapsible=0;dropTarget=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;top=0;left=0;right=0;bottom=0;" parent="sBk4UZyAnzUuyrO-kaQZ-611" vertex="1">
                    <mxGeometry y="60" width="200" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="sBk4UZyAnzUuyrO-kaQZ-616" value="" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;editable=1;overflow=hidden;" parent="sBk4UZyAnzUuyrO-kaQZ-615" vertex="1">
                    <mxGeometry width="30" height="30" as="geometry">
                        <mxRectangle width="30" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="sBk4UZyAnzUuyrO-kaQZ-617" value="算力用户名称" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;align=left;spacingLeft=6;overflow=hidden;" parent="sBk4UZyAnzUuyrO-kaQZ-615" vertex="1">
                    <mxGeometry x="30" width="170" height="30" as="geometry">
                        <mxRectangle width="170" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="sBk4UZyAnzUuyrO-kaQZ-618" value="" style="shape=tableRow;horizontal=0;startSize=0;swimlaneHead=0;swimlaneBody=0;fillColor=none;collapsible=0;dropTarget=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;top=0;left=0;right=0;bottom=0;" parent="sBk4UZyAnzUuyrO-kaQZ-611" vertex="1">
                    <mxGeometry y="90" width="200" height="34" as="geometry"/>
                </mxCell>
                <mxCell id="sBk4UZyAnzUuyrO-kaQZ-619" value="" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;editable=1;overflow=hidden;" parent="sBk4UZyAnzUuyrO-kaQZ-618" vertex="1">
                    <mxGeometry width="30" height="34" as="geometry">
                        <mxRectangle width="30" height="34" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="sBk4UZyAnzUuyrO-kaQZ-620" value="算力余额" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;align=left;spacingLeft=6;overflow=hidden;" parent="sBk4UZyAnzUuyrO-kaQZ-618" vertex="1">
                    <mxGeometry x="30" width="170" height="34" as="geometry">
                        <mxRectangle width="170" height="34" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="sBk4UZyAnzUuyrO-kaQZ-621" value="" style="shape=tableRow;horizontal=0;startSize=0;swimlaneHead=0;swimlaneBody=0;fillColor=none;collapsible=0;dropTarget=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;top=0;left=0;right=0;bottom=0;" parent="sBk4UZyAnzUuyrO-kaQZ-611" vertex="1">
                    <mxGeometry y="124" width="200" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="sBk4UZyAnzUuyrO-kaQZ-622" value="" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;editable=1;overflow=hidden;" parent="sBk4UZyAnzUuyrO-kaQZ-621" vertex="1">
                    <mxGeometry width="30" height="30" as="geometry">
                        <mxRectangle width="30" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="sBk4UZyAnzUuyrO-kaQZ-623" value="基本字段" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;align=left;spacingLeft=6;overflow=hidden;" parent="sBk4UZyAnzUuyrO-kaQZ-621" vertex="1">
                    <mxGeometry x="30" width="170" height="30" as="geometry">
                        <mxRectangle width="170" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="sBk4UZyAnzUuyrO-kaQZ-624" value="算力点记录表" style="shape=table;startSize=30;container=1;collapsible=1;childLayout=tableLayout;fixedRows=1;rowLines=0;fontStyle=1;align=center;resizeLast=1;" parent="1" vertex="1">
                    <mxGeometry x="1041" y="810" width="200" height="240" as="geometry"/>
                </mxCell>
                <mxCell id="sBk4UZyAnzUuyrO-kaQZ-625" value="" style="shape=tableRow;horizontal=0;startSize=0;swimlaneHead=0;swimlaneBody=0;fillColor=none;collapsible=0;dropTarget=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;top=0;left=0;right=0;bottom=1;" parent="sBk4UZyAnzUuyrO-kaQZ-624" vertex="1">
                    <mxGeometry y="30" width="200" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="sBk4UZyAnzUuyrO-kaQZ-626" value="PK" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;fontStyle=1;overflow=hidden;" parent="sBk4UZyAnzUuyrO-kaQZ-625" vertex="1">
                    <mxGeometry width="30" height="30" as="geometry">
                        <mxRectangle width="30" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="sBk4UZyAnzUuyrO-kaQZ-627" value="算力点记录ID" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;align=left;spacingLeft=6;fontStyle=5;overflow=hidden;" parent="sBk4UZyAnzUuyrO-kaQZ-625" vertex="1">
                    <mxGeometry x="30" width="170" height="30" as="geometry">
                        <mxRectangle width="170" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="sBk4UZyAnzUuyrO-kaQZ-628" value="" style="shape=tableRow;horizontal=0;startSize=0;swimlaneHead=0;swimlaneBody=0;fillColor=none;collapsible=0;dropTarget=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;top=0;left=0;right=0;bottom=0;" parent="sBk4UZyAnzUuyrO-kaQZ-624" vertex="1">
                    <mxGeometry y="60" width="200" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="sBk4UZyAnzUuyrO-kaQZ-629" value="" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;fontStyle=0;overflow=hidden;" parent="sBk4UZyAnzUuyrO-kaQZ-628" vertex="1">
                    <mxGeometry width="30" height="30" as="geometry">
                        <mxRectangle width="30" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="sBk4UZyAnzUuyrO-kaQZ-630" value="算力用户名称" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;align=left;spacingLeft=6;fontStyle=0;overflow=hidden;" parent="sBk4UZyAnzUuyrO-kaQZ-628" vertex="1">
                    <mxGeometry x="30" width="170" height="30" as="geometry">
                        <mxRectangle width="170" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="sBk4UZyAnzUuyrO-kaQZ-631" value="" style="shape=tableRow;horizontal=0;startSize=0;swimlaneHead=0;swimlaneBody=0;fillColor=none;collapsible=0;dropTarget=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;top=0;left=0;right=0;bottom=0;" parent="sBk4UZyAnzUuyrO-kaQZ-624" vertex="1">
                    <mxGeometry y="90" width="200" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="sBk4UZyAnzUuyrO-kaQZ-632" value="" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;editable=1;overflow=hidden;" parent="sBk4UZyAnzUuyrO-kaQZ-631" vertex="1">
                    <mxGeometry width="30" height="30" as="geometry">
                        <mxRectangle width="30" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="sBk4UZyAnzUuyrO-kaQZ-633" value="消费模块" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;align=left;spacingLeft=6;overflow=hidden;" parent="sBk4UZyAnzUuyrO-kaQZ-631" vertex="1">
                    <mxGeometry x="30" width="170" height="30" as="geometry">
                        <mxRectangle width="170" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="sBk4UZyAnzUuyrO-kaQZ-634" value="" style="shape=tableRow;horizontal=0;startSize=0;swimlaneHead=0;swimlaneBody=0;fillColor=none;collapsible=0;dropTarget=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;top=0;left=0;right=0;bottom=0;" parent="sBk4UZyAnzUuyrO-kaQZ-624" vertex="1">
                    <mxGeometry y="120" width="200" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="sBk4UZyAnzUuyrO-kaQZ-635" value="" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;editable=1;overflow=hidden;" parent="sBk4UZyAnzUuyrO-kaQZ-634" vertex="1">
                    <mxGeometry width="30" height="30" as="geometry">
                        <mxRectangle width="30" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="sBk4UZyAnzUuyrO-kaQZ-636" value="操作业务（0=充值 1消费）" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;align=left;spacingLeft=6;overflow=hidden;" parent="sBk4UZyAnzUuyrO-kaQZ-634" vertex="1">
                    <mxGeometry x="30" width="170" height="30" as="geometry">
                        <mxRectangle width="170" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="sBk4UZyAnzUuyrO-kaQZ-637" value="" style="shape=tableRow;horizontal=0;startSize=0;swimlaneHead=0;swimlaneBody=0;fillColor=none;collapsible=0;dropTarget=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;top=0;left=0;right=0;bottom=0;" parent="sBk4UZyAnzUuyrO-kaQZ-624" vertex="1">
                    <mxGeometry y="150" width="200" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="sBk4UZyAnzUuyrO-kaQZ-638" value="" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;editable=1;overflow=hidden;" parent="sBk4UZyAnzUuyrO-kaQZ-637" vertex="1">
                    <mxGeometry width="30" height="30" as="geometry">
                        <mxRectangle width="30" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="sBk4UZyAnzUuyrO-kaQZ-639" value="消费算力点" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;align=left;spacingLeft=6;overflow=hidden;" parent="sBk4UZyAnzUuyrO-kaQZ-637" vertex="1">
                    <mxGeometry x="30" width="170" height="30" as="geometry">
                        <mxRectangle width="170" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="sBk4UZyAnzUuyrO-kaQZ-640" value="" style="shape=tableRow;horizontal=0;startSize=0;swimlaneHead=0;swimlaneBody=0;fillColor=none;collapsible=0;dropTarget=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;top=0;left=0;right=0;bottom=0;" parent="sBk4UZyAnzUuyrO-kaQZ-624" vertex="1">
                    <mxGeometry y="180" width="200" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="sBk4UZyAnzUuyrO-kaQZ-641" value="" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;editable=1;overflow=hidden;" parent="sBk4UZyAnzUuyrO-kaQZ-640" vertex="1">
                    <mxGeometry width="30" height="30" as="geometry">
                        <mxRectangle width="30" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="sBk4UZyAnzUuyrO-kaQZ-642" value="剩余算力点" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;align=left;spacingLeft=6;overflow=hidden;" parent="sBk4UZyAnzUuyrO-kaQZ-640" vertex="1">
                    <mxGeometry x="30" width="170" height="30" as="geometry">
                        <mxRectangle width="170" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="sBk4UZyAnzUuyrO-kaQZ-643" value="" style="shape=tableRow;horizontal=0;startSize=0;swimlaneHead=0;swimlaneBody=0;fillColor=none;collapsible=0;dropTarget=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;top=0;left=0;right=0;bottom=0;" parent="sBk4UZyAnzUuyrO-kaQZ-624" vertex="1">
                    <mxGeometry y="210" width="200" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="sBk4UZyAnzUuyrO-kaQZ-644" value="" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;editable=1;overflow=hidden;" parent="sBk4UZyAnzUuyrO-kaQZ-643" vertex="1">
                    <mxGeometry width="30" height="30" as="geometry">
                        <mxRectangle width="30" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="sBk4UZyAnzUuyrO-kaQZ-645" value="基本字段" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;align=left;spacingLeft=6;overflow=hidden;" parent="sBk4UZyAnzUuyrO-kaQZ-643" vertex="1">
                    <mxGeometry x="30" width="170" height="30" as="geometry">
                        <mxRectangle width="170" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="JcV92SRO5VpQxQLhwCca-523" value="声音" style="shape=table;startSize=30;container=1;collapsible=1;childLayout=tableLayout;fixedRows=1;rowLines=0;fontStyle=1;align=center;resizeLast=1;" parent="1" vertex="1">
                    <mxGeometry x="870" y="-100" width="280" height="300" as="geometry"/>
                </mxCell>
                <mxCell id="JcV92SRO5VpQxQLhwCca-524" value="" style="shape=tableRow;horizontal=0;startSize=0;swimlaneHead=0;swimlaneBody=0;fillColor=none;collapsible=0;dropTarget=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;top=0;left=0;right=0;bottom=1;" parent="JcV92SRO5VpQxQLhwCca-523" vertex="1">
                    <mxGeometry y="30" width="280" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="JcV92SRO5VpQxQLhwCca-525" value="PK" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;fontStyle=1;overflow=hidden;" parent="JcV92SRO5VpQxQLhwCca-524" vertex="1">
                    <mxGeometry width="30" height="30" as="geometry">
                        <mxRectangle width="30" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="JcV92SRO5VpQxQLhwCca-526" value="声音ID" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;align=left;spacingLeft=6;fontStyle=5;overflow=hidden;" parent="JcV92SRO5VpQxQLhwCca-524" vertex="1">
                    <mxGeometry x="30" width="250" height="30" as="geometry">
                        <mxRectangle width="250" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="JcV92SRO5VpQxQLhwCca-527" value="" style="shape=tableRow;horizontal=0;startSize=0;swimlaneHead=0;swimlaneBody=0;fillColor=none;collapsible=0;dropTarget=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;top=0;left=0;right=0;bottom=0;" parent="JcV92SRO5VpQxQLhwCca-523" vertex="1">
                    <mxGeometry y="60" width="280" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="JcV92SRO5VpQxQLhwCca-528" value="" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;editable=1;overflow=hidden;" parent="JcV92SRO5VpQxQLhwCca-527" vertex="1">
                    <mxGeometry width="30" height="30" as="geometry">
                        <mxRectangle width="30" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="JcV92SRO5VpQxQLhwCca-529" value="声音名称" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;align=left;spacingLeft=6;overflow=hidden;" parent="JcV92SRO5VpQxQLhwCca-527" vertex="1">
                    <mxGeometry x="30" width="250" height="30" as="geometry">
                        <mxRectangle width="250" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="JcV92SRO5VpQxQLhwCca-530" value="" style="shape=tableRow;horizontal=0;startSize=0;swimlaneHead=0;swimlaneBody=0;fillColor=none;collapsible=0;dropTarget=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;top=0;left=0;right=0;bottom=0;" parent="JcV92SRO5VpQxQLhwCca-523" vertex="1">
                    <mxGeometry y="90" width="280" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="JcV92SRO5VpQxQLhwCca-531" value="" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;editable=1;overflow=hidden;" parent="JcV92SRO5VpQxQLhwCca-530" vertex="1">
                    <mxGeometry width="30" height="30" as="geometry">
                        <mxRectangle width="30" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="JcV92SRO5VpQxQLhwCca-532" value="声音状态&#xa;（0=待训练，1=队列中，2=完成，3=失败）" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;align=left;spacingLeft=6;overflow=hidden;" parent="JcV92SRO5VpQxQLhwCca-530" vertex="1">
                    <mxGeometry x="30" width="250" height="30" as="geometry">
                        <mxRectangle width="250" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="JcV92SRO5VpQxQLhwCca-533" value="" style="shape=tableRow;horizontal=0;startSize=0;swimlaneHead=0;swimlaneBody=0;fillColor=none;collapsible=0;dropTarget=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;top=0;left=0;right=0;bottom=0;" parent="JcV92SRO5VpQxQLhwCca-523" vertex="1">
                    <mxGeometry y="120" width="280" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="JcV92SRO5VpQxQLhwCca-534" value="" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;editable=1;overflow=hidden;" parent="JcV92SRO5VpQxQLhwCca-533" vertex="1">
                    <mxGeometry width="30" height="30" as="geometry">
                        <mxRectangle width="30" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="JcV92SRO5VpQxQLhwCca-535" value="声音参考音频" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;align=left;spacingLeft=6;overflow=hidden;" parent="JcV92SRO5VpQxQLhwCca-533" vertex="1">
                    <mxGeometry x="30" width="250" height="30" as="geometry">
                        <mxRectangle width="250" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="JcV92SRO5VpQxQLhwCca-536" value="" style="shape=tableRow;horizontal=0;startSize=0;swimlaneHead=0;swimlaneBody=0;fillColor=none;collapsible=0;dropTarget=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;top=0;left=0;right=0;bottom=0;" parent="JcV92SRO5VpQxQLhwCca-523" vertex="1">
                    <mxGeometry y="150" width="280" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="JcV92SRO5VpQxQLhwCca-537" value="" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;editable=1;overflow=hidden;" parent="JcV92SRO5VpQxQLhwCca-536" vertex="1">
                    <mxGeometry width="30" height="30" as="geometry">
                        <mxRectangle width="30" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="JcV92SRO5VpQxQLhwCca-538" value="声音训练音频" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;align=left;spacingLeft=6;overflow=hidden;" parent="JcV92SRO5VpQxQLhwCca-536" vertex="1">
                    <mxGeometry x="30" width="250" height="30" as="geometry">
                        <mxRectangle width="250" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="JcV92SRO5VpQxQLhwCca-539" value="" style="shape=tableRow;horizontal=0;startSize=0;swimlaneHead=0;swimlaneBody=0;fillColor=none;collapsible=0;dropTarget=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;top=0;left=0;right=0;bottom=0;" parent="JcV92SRO5VpQxQLhwCca-523" vertex="1">
                    <mxGeometry y="180" width="280" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="JcV92SRO5VpQxQLhwCca-540" value="" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;editable=1;overflow=hidden;" parent="JcV92SRO5VpQxQLhwCca-539" vertex="1">
                    <mxGeometry width="30" height="30" as="geometry">
                        <mxRectangle width="30" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="JcV92SRO5VpQxQLhwCca-541" value="gpt模型路径" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;align=left;spacingLeft=6;overflow=hidden;" parent="JcV92SRO5VpQxQLhwCca-539" vertex="1">
                    <mxGeometry x="30" width="250" height="30" as="geometry">
                        <mxRectangle width="250" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="JcV92SRO5VpQxQLhwCca-542" value="" style="shape=tableRow;horizontal=0;startSize=0;swimlaneHead=0;swimlaneBody=0;fillColor=none;collapsible=0;dropTarget=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;top=0;left=0;right=0;bottom=0;" parent="JcV92SRO5VpQxQLhwCca-523" vertex="1">
                    <mxGeometry y="210" width="280" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="JcV92SRO5VpQxQLhwCca-543" value="" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;editable=1;overflow=hidden;" parent="JcV92SRO5VpQxQLhwCca-542" vertex="1">
                    <mxGeometry width="30" height="30" as="geometry">
                        <mxRectangle width="30" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="JcV92SRO5VpQxQLhwCca-544" value="sovits模型路径" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;align=left;spacingLeft=6;overflow=hidden;" parent="JcV92SRO5VpQxQLhwCca-542" vertex="1">
                    <mxGeometry x="30" width="250" height="30" as="geometry">
                        <mxRectangle width="250" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="JcV92SRO5VpQxQLhwCca-545" value="" style="shape=tableRow;horizontal=0;startSize=0;swimlaneHead=0;swimlaneBody=0;fillColor=none;collapsible=0;dropTarget=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;top=0;left=0;right=0;bottom=0;" parent="JcV92SRO5VpQxQLhwCca-523" vertex="1">
                    <mxGeometry y="240" width="280" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="JcV92SRO5VpQxQLhwCca-546" value="" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;editable=1;overflow=hidden;" parent="JcV92SRO5VpQxQLhwCca-545" vertex="1">
                    <mxGeometry width="30" height="30" as="geometry">
                        <mxRectangle width="30" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="JcV92SRO5VpQxQLhwCca-547" value="参考文本" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;align=left;spacingLeft=6;overflow=hidden;" parent="JcV92SRO5VpQxQLhwCca-545" vertex="1">
                    <mxGeometry x="30" width="250" height="30" as="geometry">
                        <mxRectangle width="250" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="JcV92SRO5VpQxQLhwCca-548" value="" style="shape=tableRow;horizontal=0;startSize=0;swimlaneHead=0;swimlaneBody=0;fillColor=none;collapsible=0;dropTarget=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;top=0;left=0;right=0;bottom=0;" parent="JcV92SRO5VpQxQLhwCca-523" vertex="1">
                    <mxGeometry y="270" width="280" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="JcV92SRO5VpQxQLhwCca-549" value="" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;editable=1;overflow=hidden;" parent="JcV92SRO5VpQxQLhwCca-548" vertex="1">
                    <mxGeometry width="30" height="30" as="geometry">
                        <mxRectangle width="30" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="JcV92SRO5VpQxQLhwCca-550" value="基本字段" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;align=left;spacingLeft=6;overflow=hidden;" parent="JcV92SRO5VpQxQLhwCca-548" vertex="1">
                    <mxGeometry x="30" width="250" height="30" as="geometry">
                        <mxRectangle width="250" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="JcV92SRO5VpQxQLhwCca-552" value="" style="edgeStyle=entityRelationEdgeStyle;fontSize=12;html=1;endArrow=ERzeroToMany;endFill=1;" parent="1" edge="1">
                    <mxGeometry width="100" height="100" relative="1" as="geometry">
                        <mxPoint x="780" y="20" as="sourcePoint"/>
                        <mxPoint x="870" y="52" as="targetPoint"/>
                        <Array as="points">
                            <mxPoint x="901" y="400"/>
                            <mxPoint x="855" y="35"/>
                            <mxPoint x="870" y="525"/>
                            <mxPoint x="880" y="40"/>
                            <mxPoint x="870" y="535"/>
                            <mxPoint x="870" y="585"/>
                            <mxPoint x="880" y="585"/>
                            <mxPoint x="860" y="575"/>
                        </Array>
                    </mxGeometry>
                </mxCell>
                <mxCell id="236" value="任务队列" style="shape=table;startSize=30;container=1;collapsible=1;childLayout=tableLayout;fixedRows=1;rowLines=0;fontStyle=1;align=center;resizeLast=1;" parent="1" vertex="1">
                    <mxGeometry x="1241" y="-400" width="360" height="310" as="geometry"/>
                </mxCell>
                <mxCell id="237" value="" style="shape=tableRow;horizontal=0;startSize=0;swimlaneHead=0;swimlaneBody=0;fillColor=none;collapsible=0;dropTarget=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;top=0;left=0;right=0;bottom=1;" parent="236" vertex="1">
                    <mxGeometry y="30" width="360" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="238" value="PK" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;fontStyle=1;overflow=hidden;" parent="237" vertex="1">
                    <mxGeometry width="30" height="30" as="geometry">
                        <mxRectangle width="30" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="239" value="任务ID" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;align=left;spacingLeft=6;fontStyle=5;overflow=hidden;" parent="237" vertex="1">
                    <mxGeometry x="30" width="330" height="30" as="geometry">
                        <mxRectangle width="330" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="240" value="" style="shape=tableRow;horizontal=0;startSize=0;swimlaneHead=0;swimlaneBody=0;fillColor=none;collapsible=0;dropTarget=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;top=0;left=0;right=0;bottom=0;" parent="236" vertex="1">
                    <mxGeometry y="60" width="360" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="241" value="" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;editable=1;overflow=hidden;" parent="240" vertex="1">
                    <mxGeometry width="30" height="30" as="geometry">
                        <mxRectangle width="30" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="242" value="任务名称" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;align=left;spacingLeft=6;overflow=hidden;" parent="240" vertex="1">
                    <mxGeometry x="30" width="330" height="30" as="geometry">
                        <mxRectangle width="330" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="472" value="" style="shape=tableRow;horizontal=0;startSize=0;swimlaneHead=0;swimlaneBody=0;fillColor=none;collapsible=0;dropTarget=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;top=0;left=0;right=0;bottom=0;" parent="236" vertex="1">
                    <mxGeometry y="90" width="360" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="473" value="" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;editable=1;overflow=hidden;" parent="472" vertex="1">
                    <mxGeometry width="30" height="30" as="geometry">
                        <mxRectangle width="30" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="474" value="任务类型（0=训练，1=推理）" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;align=left;spacingLeft=6;overflow=hidden;" parent="472" vertex="1">
                    <mxGeometry x="30" width="330" height="30" as="geometry">
                        <mxRectangle width="330" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="475" value="" style="shape=tableRow;horizontal=0;startSize=0;swimlaneHead=0;swimlaneBody=0;fillColor=none;collapsible=0;dropTarget=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;top=0;left=0;right=0;bottom=0;" parent="236" vertex="1">
                    <mxGeometry y="120" width="360" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="476" value="" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;editable=1;overflow=hidden;" parent="475" vertex="1">
                    <mxGeometry width="30" height="30" as="geometry">
                        <mxRectangle width="30" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="477" value="模型名称" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;align=left;spacingLeft=6;overflow=hidden;" parent="475" vertex="1">
                    <mxGeometry x="30" width="330" height="30" as="geometry">
                        <mxRectangle width="330" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="243" value="" style="shape=tableRow;horizontal=0;startSize=0;swimlaneHead=0;swimlaneBody=0;fillColor=none;collapsible=0;dropTarget=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;top=0;left=0;right=0;bottom=0;" parent="236" vertex="1">
                    <mxGeometry y="150" width="360" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="244" value="" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;editable=1;overflow=hidden;" parent="243" vertex="1">
                    <mxGeometry width="30" height="30" as="geometry">
                        <mxRectangle width="30" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="245" value="任务状态（0=待处理，1=处理中，2=完成，3=失败）" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;align=left;spacingLeft=6;overflow=hidden;" parent="243" vertex="1">
                    <mxGeometry x="30" width="330" height="30" as="geometry">
                        <mxRectangle width="330" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="321" value="" style="shape=tableRow;horizontal=0;startSize=0;swimlaneHead=0;swimlaneBody=0;fillColor=none;collapsible=0;dropTarget=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;top=0;left=0;right=0;bottom=0;" parent="236" vertex="1">
                    <mxGeometry y="180" width="360" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="322" value="" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;editable=1;overflow=hidden;" parent="321" vertex="1">
                    <mxGeometry width="30" height="30" as="geometry">
                        <mxRectangle width="30" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="323" value="任务内容（JSON）" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;align=left;spacingLeft=6;overflow=hidden;" parent="321" vertex="1">
                    <mxGeometry x="30" width="330" height="30" as="geometry">
                        <mxRectangle width="330" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="337" value="" style="shape=tableRow;horizontal=0;startSize=0;swimlaneHead=0;swimlaneBody=0;fillColor=none;collapsible=0;dropTarget=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;top=0;left=0;right=0;bottom=0;" parent="236" vertex="1">
                    <mxGeometry y="210" width="360" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="338" value="" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;editable=1;overflow=hidden;" parent="337" vertex="1">
                    <mxGeometry width="30" height="30" as="geometry">
                        <mxRectangle width="30" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="339" value="任务结果（JSON）" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;align=left;spacingLeft=6;overflow=hidden;" parent="337" vertex="1">
                    <mxGeometry x="30" width="330" height="30" as="geometry">
                        <mxRectangle width="330" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="334" value="" style="shape=tableRow;horizontal=0;startSize=0;swimlaneHead=0;swimlaneBody=0;fillColor=none;collapsible=0;dropTarget=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;top=0;left=0;right=0;bottom=0;" parent="236" vertex="1">
                    <mxGeometry y="240" width="360" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="335" value="" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;editable=1;overflow=hidden;" parent="334" vertex="1">
                    <mxGeometry width="30" height="30" as="geometry">
                        <mxRectangle width="30" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="336" value="机器码" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;align=left;spacingLeft=6;overflow=hidden;" parent="334" vertex="1">
                    <mxGeometry x="30" width="330" height="30" as="geometry">
                        <mxRectangle width="330" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="246" value="" style="shape=tableRow;horizontal=0;startSize=0;swimlaneHead=0;swimlaneBody=0;fillColor=none;collapsible=0;dropTarget=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;top=0;left=0;right=0;bottom=0;" parent="236" vertex="1">
                    <mxGeometry y="270" width="360" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="247" value="" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;editable=1;overflow=hidden;" parent="246" vertex="1">
                    <mxGeometry width="30" height="40" as="geometry">
                        <mxRectangle width="30" height="40" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="248" value="基本字段" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;align=left;spacingLeft=6;overflow=hidden;movable=0;resizable=0;rotatable=0;deletable=0;editable=0;locked=1;" parent="246" vertex="1">
                    <mxGeometry x="30" width="330" height="40" as="geometry">
                        <mxRectangle width="330" height="40" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="617" value="模型" style="shape=table;startSize=30;container=1;collapsible=1;childLayout=tableLayout;fixedRows=1;rowLines=0;fontStyle=1;align=center;resizeLast=1;" parent="1" vertex="1">
                    <mxGeometry x="600" y="1065" width="200" height="218" as="geometry"/>
                </mxCell>
                <mxCell id="618" value="" style="shape=tableRow;horizontal=0;startSize=0;swimlaneHead=0;swimlaneBody=0;fillColor=none;collapsible=0;dropTarget=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;top=0;left=0;right=0;bottom=1;" parent="617" vertex="1">
                    <mxGeometry y="30" width="200" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="619" value="PK" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;fontStyle=1;overflow=hidden;" parent="618" vertex="1">
                    <mxGeometry width="30" height="30" as="geometry">
                        <mxRectangle width="30" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="620" value="模型ID" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;align=left;spacingLeft=6;fontStyle=5;overflow=hidden;" parent="618" vertex="1">
                    <mxGeometry x="30" width="170" height="30" as="geometry">
                        <mxRectangle width="170" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="621" value="" style="shape=tableRow;horizontal=0;startSize=0;swimlaneHead=0;swimlaneBody=0;fillColor=none;collapsible=0;dropTarget=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;top=0;left=0;right=0;bottom=0;" parent="617" vertex="1">
                    <mxGeometry y="60" width="200" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="622" value="" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;editable=1;overflow=hidden;" parent="621" vertex="1">
                    <mxGeometry width="30" height="30" as="geometry">
                        <mxRectangle width="30" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="623" value="模型编码" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;align=left;spacingLeft=6;overflow=hidden;" parent="621" vertex="1">
                    <mxGeometry x="30" width="170" height="30" as="geometry">
                        <mxRectangle width="170" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="624" value="" style="shape=tableRow;horizontal=0;startSize=0;swimlaneHead=0;swimlaneBody=0;fillColor=none;collapsible=0;dropTarget=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;top=0;left=0;right=0;bottom=0;" parent="617" vertex="1">
                    <mxGeometry y="90" width="200" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="625" value="" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;editable=1;overflow=hidden;" parent="624" vertex="1">
                    <mxGeometry width="30" height="30" as="geometry">
                        <mxRectangle width="30" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="626" value="模型名称" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;align=left;spacingLeft=6;overflow=hidden;" parent="624" vertex="1">
                    <mxGeometry x="30" width="170" height="30" as="geometry">
                        <mxRectangle width="170" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="627" value="" style="shape=tableRow;horizontal=0;startSize=0;swimlaneHead=0;swimlaneBody=0;fillColor=none;collapsible=0;dropTarget=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;top=0;left=0;right=0;bottom=0;" parent="617" vertex="1">
                    <mxGeometry y="120" width="200" height="34" as="geometry"/>
                </mxCell>
                <mxCell id="628" value="" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;editable=1;overflow=hidden;" parent="627" vertex="1">
                    <mxGeometry width="30" height="34" as="geometry">
                        <mxRectangle width="30" height="34" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="629" value="模型状态（0=停用 1=正常）" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;align=left;spacingLeft=6;overflow=hidden;" parent="627" vertex="1">
                    <mxGeometry x="30" width="170" height="34" as="geometry">
                        <mxRectangle width="170" height="34" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="630" value="" style="shape=tableRow;horizontal=0;startSize=0;swimlaneHead=0;swimlaneBody=0;fillColor=none;collapsible=0;dropTarget=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;top=0;left=0;right=0;bottom=0;" parent="617" vertex="1">
                    <mxGeometry y="154" width="200" height="34" as="geometry"/>
                </mxCell>
                <mxCell id="631" value="" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;editable=1;overflow=hidden;" parent="630" vertex="1">
                    <mxGeometry width="30" height="34" as="geometry">
                        <mxRectangle width="30" height="34" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="632" value="模型价位" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;align=left;spacingLeft=6;overflow=hidden;" parent="630" vertex="1">
                    <mxGeometry x="30" width="170" height="34" as="geometry">
                        <mxRectangle width="170" height="34" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="633" value="" style="shape=tableRow;horizontal=0;startSize=0;swimlaneHead=0;swimlaneBody=0;fillColor=none;collapsible=0;dropTarget=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;top=0;left=0;right=0;bottom=0;" parent="617" vertex="1">
                    <mxGeometry y="188" width="200" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="634" value="" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;editable=1;overflow=hidden;" parent="633" vertex="1">
                    <mxGeometry width="30" height="30" as="geometry">
                        <mxRectangle width="30" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="635" value="基本字段" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;align=left;spacingLeft=6;overflow=hidden;" parent="633" vertex="1">
                    <mxGeometry x="30" width="170" height="30" as="geometry">
                        <mxRectangle width="170" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="636" value="视频合成" style="shape=table;startSize=30;container=1;collapsible=1;childLayout=tableLayout;fixedRows=1;rowLines=0;fontStyle=1;align=center;resizeLast=1;" parent="1" vertex="1">
                    <mxGeometry x="820" y="1091" width="279" height="422" as="geometry"/>
                </mxCell>
                <mxCell id="637" value="" style="shape=tableRow;horizontal=0;startSize=0;swimlaneHead=0;swimlaneBody=0;fillColor=none;collapsible=0;dropTarget=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;top=0;left=0;right=0;bottom=1;" parent="636" vertex="1">
                    <mxGeometry y="30" width="279" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="638" value="PK" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;fontStyle=1;overflow=hidden;" parent="637" vertex="1">
                    <mxGeometry width="30" height="30" as="geometry">
                        <mxRectangle width="30" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="639" value="ID" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;align=left;spacingLeft=6;fontStyle=5;overflow=hidden;" parent="637" vertex="1">
                    <mxGeometry x="30" width="249" height="30" as="geometry">
                        <mxRectangle width="249" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="640" value="" style="shape=tableRow;horizontal=0;startSize=0;swimlaneHead=0;swimlaneBody=0;fillColor=none;collapsible=0;dropTarget=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;top=0;left=0;right=0;bottom=0;" parent="636" vertex="1">
                    <mxGeometry y="60" width="279" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="641" value="" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;editable=1;overflow=hidden;" parent="640" vertex="1">
                    <mxGeometry width="30" height="30" as="geometry">
                        <mxRectangle width="30" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="642" value="视频名称" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;align=left;spacingLeft=6;overflow=hidden;" parent="640" vertex="1">
                    <mxGeometry x="30" width="249" height="30" as="geometry">
                        <mxRectangle width="249" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="643" value="" style="shape=tableRow;horizontal=0;startSize=0;swimlaneHead=0;swimlaneBody=0;fillColor=none;collapsible=0;dropTarget=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;top=0;left=0;right=0;bottom=0;" parent="636" vertex="1">
                    <mxGeometry y="90" width="279" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="644" value="" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;editable=1;overflow=hidden;" parent="643" vertex="1">
                    <mxGeometry width="30" height="30" as="geometry">
                        <mxRectangle width="30" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="645" value="驱动音频" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;align=left;spacingLeft=6;overflow=hidden;" parent="643" vertex="1">
                    <mxGeometry x="30" width="249" height="30" as="geometry">
                        <mxRectangle width="249" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="646" value="" style="shape=tableRow;horizontal=0;startSize=0;swimlaneHead=0;swimlaneBody=0;fillColor=none;collapsible=0;dropTarget=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;top=0;left=0;right=0;bottom=0;" parent="636" vertex="1">
                    <mxGeometry y="120" width="279" height="34" as="geometry"/>
                </mxCell>
                <mxCell id="647" value="" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;editable=1;overflow=hidden;" parent="646" vertex="1">
                    <mxGeometry width="30" height="34" as="geometry">
                        <mxRectangle width="30" height="34" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="648" value="驱动视频" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;align=left;spacingLeft=6;overflow=hidden;" parent="646" vertex="1">
                    <mxGeometry x="30" width="249" height="34" as="geometry">
                        <mxRectangle width="249" height="34" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="649" value="" style="shape=tableRow;horizontal=0;startSize=0;swimlaneHead=0;swimlaneBody=0;fillColor=none;collapsible=0;dropTarget=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;top=0;left=0;right=0;bottom=0;" parent="636" vertex="1">
                    <mxGeometry y="154" width="279" height="34" as="geometry"/>
                </mxCell>
                <mxCell id="650" value="" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;editable=1;overflow=hidden;" parent="649" vertex="1">
                    <mxGeometry width="30" height="34" as="geometry">
                        <mxRectangle width="30" height="34" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="651" value="结果视频" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;align=left;spacingLeft=6;overflow=hidden;" parent="649" vertex="1">
                    <mxGeometry x="30" width="249" height="34" as="geometry">
                        <mxRectangle width="249" height="34" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="652" value="" style="shape=tableRow;horizontal=0;startSize=0;swimlaneHead=0;swimlaneBody=0;fillColor=none;collapsible=0;dropTarget=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;top=0;left=0;right=0;bottom=0;" parent="636" vertex="1">
                    <mxGeometry y="188" width="279" height="34" as="geometry"/>
                </mxCell>
                <mxCell id="653" value="" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;editable=1;overflow=hidden;" parent="652" vertex="1">
                    <mxGeometry width="30" height="34" as="geometry">
                        <mxRectangle width="30" height="34" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="654" value="合成完成时间" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;align=left;spacingLeft=6;overflow=hidden;" parent="652" vertex="1">
                    <mxGeometry x="30" width="249" height="34" as="geometry">
                        <mxRectangle width="249" height="34" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="655" value="" style="shape=tableRow;horizontal=0;startSize=0;swimlaneHead=0;swimlaneBody=0;fillColor=none;collapsible=0;dropTarget=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;top=0;left=0;right=0;bottom=0;" parent="636" vertex="1">
                    <mxGeometry y="222" width="279" height="34" as="geometry"/>
                </mxCell>
                <mxCell id="656" value="" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;editable=1;overflow=hidden;" parent="655" vertex="1">
                    <mxGeometry width="30" height="34" as="geometry">
                        <mxRectangle width="30" height="34" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="657" value="视频状态(1待处理 2处理中 3成功 4失败)" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;align=left;spacingLeft=6;overflow=hidden;" parent="655" vertex="1">
                    <mxGeometry x="30" width="249" height="34" as="geometry">
                        <mxRectangle width="249" height="34" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="658" value="" style="shape=tableRow;horizontal=0;startSize=0;swimlaneHead=0;swimlaneBody=0;fillColor=none;collapsible=0;dropTarget=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;top=0;left=0;right=0;bottom=0;" parent="636" vertex="1">
                    <mxGeometry y="256" width="279" height="34" as="geometry"/>
                </mxCell>
                <mxCell id="659" value="" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;editable=1;overflow=hidden;" parent="658" vertex="1">
                    <mxGeometry width="30" height="34" as="geometry">
                        <mxRectangle width="30" height="34" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="660" value="模型名称" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;align=left;spacingLeft=6;overflow=hidden;" parent="658" vertex="1">
                    <mxGeometry x="30" width="249" height="34" as="geometry">
                        <mxRectangle width="249" height="34" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="661" value="" style="shape=tableRow;horizontal=0;startSize=0;swimlaneHead=0;swimlaneBody=0;fillColor=none;collapsible=0;dropTarget=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;top=0;left=0;right=0;bottom=0;" parent="636" vertex="1">
                    <mxGeometry y="290" width="279" height="34" as="geometry"/>
                </mxCell>
                <mxCell id="662" value="" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;editable=1;overflow=hidden;" parent="661" vertex="1">
                    <mxGeometry width="30" height="34" as="geometry">
                        <mxRectangle width="30" height="34" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="663" value="服务端任务编号" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;align=left;spacingLeft=6;overflow=hidden;" parent="661" vertex="1">
                    <mxGeometry x="30" width="249" height="34" as="geometry">
                        <mxRectangle width="249" height="34" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="664" value="" style="shape=tableRow;horizontal=0;startSize=0;swimlaneHead=0;swimlaneBody=0;fillColor=none;collapsible=0;dropTarget=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;top=0;left=0;right=0;bottom=0;" parent="636" vertex="1">
                    <mxGeometry y="324" width="279" height="34" as="geometry"/>
                </mxCell>
                <mxCell id="665" value="" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;editable=1;overflow=hidden;" parent="664" vertex="1">
                    <mxGeometry width="30" height="34" as="geometry">
                        <mxRectangle width="30" height="34" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="666" value="回调URL" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;align=left;spacingLeft=6;overflow=hidden;" parent="664" vertex="1">
                    <mxGeometry x="30" width="249" height="34" as="geometry">
                        <mxRectangle width="249" height="34" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="667" value="" style="shape=tableRow;horizontal=0;startSize=0;swimlaneHead=0;swimlaneBody=0;fillColor=none;collapsible=0;dropTarget=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;top=0;left=0;right=0;bottom=0;" parent="636" vertex="1">
                    <mxGeometry y="358" width="279" height="34" as="geometry"/>
                </mxCell>
                <mxCell id="668" value="" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;editable=1;overflow=hidden;" parent="667" vertex="1">
                    <mxGeometry width="30" height="34" as="geometry">
                        <mxRectangle width="30" height="34" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="669" value="json配置信息" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;align=left;spacingLeft=6;overflow=hidden;" parent="667" vertex="1">
                    <mxGeometry x="30" width="249" height="34" as="geometry">
                        <mxRectangle width="249" height="34" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="670" value="" style="shape=tableRow;horizontal=0;startSize=0;swimlaneHead=0;swimlaneBody=0;fillColor=none;collapsible=0;dropTarget=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;top=0;left=0;right=0;bottom=0;" parent="636" vertex="1">
                    <mxGeometry y="392" width="279" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="671" value="" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;editable=1;overflow=hidden;" parent="670" vertex="1">
                    <mxGeometry width="30" height="30" as="geometry">
                        <mxRectangle width="30" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="672" value="基本字段" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;align=left;spacingLeft=6;overflow=hidden;" parent="670" vertex="1">
                    <mxGeometry x="30" width="249" height="30" as="geometry">
                        <mxRectangle width="249" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
            </root>
        </mxGraphModel>
    </diagram>
</mxfile>